#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.parse
import json
import requests
import hashlib
import time
import subprocess
from datetime import datetime

# 数据库配置
DB_CONNECTION = "*************************************************/go_kuaidi"

# API配置
API_BASE_URL = "http://localhost:8081"
CALLBACK_ENDPOINT = "/api/v1/callbacks/kuaidi100"

def get_cancelling_orders():
    """获取所有取消中状态的快递100订单"""
    try:
        query = """
        SELECT
            id, platform_order_no, customer_order_no, tracking_no, order_no,
            express_type, task_id, poll_token, user_id
        FROM order_records
        WHERE provider = 'kuaidi100'
            AND status = 'cancelling'
        ORDER BY created_at DESC;
        """

        result = subprocess.run([
            'psql', DB_CONNECTION, '-t', '-c', query
        ], capture_output=True, text=True, check=True)

        orders = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = [part.strip() for part in line.split('|')]
                if len(parts) >= 9:
                    orders.append(parts)

        return orders
    except Exception as e:
        print(f"❌ 获取订单列表失败: {str(e)}")
        return []

def generate_kuaidi100_cancel_callback(order_info):
    """生成快递100已取消回调数据"""
    callback_data = {
        "data": {
            "orderId": order_info['order_no'],
            "courierName": "系统取消",
            "courierMobile": "",
            "defPrice": None,
            "mktId": None,
            "price": None,
            "freight": None,
            "weight": None,
            "pollToken": order_info['poll_token'],
            "thirdOrderId": order_info['platform_order_no'],
            "status": 99,  # 99 = 已取消
            "updateTime": int(time.time() * 1000),  # 当前时间戳（毫秒）
            "sentStatus": 99,  # 发送状态：已取消
            "userCancel": True,  # 用户主动取消
            "payStatus": 0,  # 支付状态
            "trace": "订单已取消"
        },
        "kuaidicom": order_info['express_type'].lower(),
        "kuaidinum": order_info['tracking_no'],
        "message": "成功",
        "status": "200"
    }
    
    return callback_data

def generate_callback_signature(param_json, task_id):
    """生成快递100回调签名（模拟）"""
    sign_string = f"{param_json}{task_id}"
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()

def send_cancel_callback(order_info):
    """发送取消回调"""
    try:
        # 1. 生成回调数据
        callback_data = generate_kuaidi100_cancel_callback(order_info)
        param_json = json.dumps(callback_data, separators=(',', ':'), ensure_ascii=False)
        
        # 2. 生成签名
        signature = generate_callback_signature(param_json, order_info['task_id'])
        
        # 3. 构建POST数据
        post_data = {
            "param": param_json,
            "sign": signature,
            "taskId": order_info['task_id']
        }
        
        # URL编码
        encoded_data = urllib.parse.urlencode(post_data, quote_via=urllib.parse.quote)
        
        # 4. 发送请求
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Kuaidi100-Batch-Cancel-Simulation/1.0',
            'X-Simulate-Callback': 'true',
            'X-Cancel-Order': order_info['tracking_no'],
            'X-Batch-Process': 'true',
            'X-Simulate-Timestamp': str(int(time.time()))
        }
        
        response = requests.post(
            f"{API_BASE_URL}{CALLBACK_ENDPOINT}",
            data=encoded_data,
            headers=headers,
            timeout=30
        )
        
        return response.status_code == 200, response.text
        
    except Exception as e:
        return False, str(e)

def verify_order_status(tracking_no):
    """验证订单状态是否已更新"""
    try:
        query = f"""
        SELECT status, updated_at AT TIME ZONE 'Asia/Shanghai' as updated_at_beijing
        FROM order_records
        WHERE tracking_no = '{tracking_no}';
        """

        result = subprocess.run([
            'psql', DB_CONNECTION, '-t', '-c', query
        ], capture_output=True, text=True, check=True)

        if result.stdout.strip():
            parts = [part.strip() for part in result.stdout.strip().split('|')]
            if len(parts) >= 2:
                return parts[0], parts[1]

        return None, None

    except Exception as e:
        print(f"❌ 验证订单状态失败: {str(e)}")
        return None, None

def main():
    """主函数"""
    print("🚀 批量模拟快递100已取消回调")
    print("=" * 80)
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 获取所有取消中的订单
    print("\n📋 获取取消中状态的订单...")
    orders = get_cancelling_orders()
    
    if not orders:
        print("❌ 没有找到取消中状态的订单")
        return
    
    print(f"✅ 找到 {len(orders)} 个取消中状态的订单")
    
    # 2. 批量处理
    success_count = 0
    failed_count = 0
    
    print(f"\n🔄 开始批量处理...")
    print("-" * 80)
    
    for i, order_row in enumerate(orders, 1):
        order_info = {
            'id': order_row[0],
            'platform_order_no': order_row[1],
            'customer_order_no': order_row[2],
            'tracking_no': order_row[3],
            'order_no': order_row[4],
            'express_type': order_row[5],
            'task_id': order_row[6],
            'poll_token': order_row[7],
            'user_id': order_row[8]
        }
        
        print(f"[{i:2d}/{len(orders)}] 处理订单: {order_info['tracking_no']} ({order_info['platform_order_no']})")
        
        # 发送取消回调
        success, response = send_cancel_callback(order_info)
        
        if success:
            print(f"         ✅ 回调发送成功")
            
            # 验证状态更新
            time.sleep(0.5)  # 等待状态更新
            status, updated_at = verify_order_status(order_info['tracking_no'])
            
            if status == 'cancelled':
                print(f"         ✅ 状态已更新为: {status} (更新时间: {updated_at})")
                success_count += 1
            else:
                print(f"         ⚠️  状态未更新，当前状态: {status}")
                success_count += 1  # 回调成功就算成功
        else:
            print(f"         ❌ 回调发送失败: {response}")
            failed_count += 1
        
        # 避免请求过快
        time.sleep(0.3)
    
    # 3. 统计结果
    print("\n" + "=" * 80)
    print("📊 批量处理结果统计:")
    print(f"   ✅ 成功处理: {success_count} 个订单")
    print(f"   ❌ 处理失败: {failed_count} 个订单")
    print(f"   📈 成功率: {success_count/(success_count+failed_count)*100:.1f}%")
    
    if success_count > 0:
        print(f"\n📝 建议后续操作:")
        print(f"   1. 检查数据库中所有订单状态是否已更新为'cancelled'")
        print(f"   2. 检查回调转发记录是否正常")
        print(f"   3. 检查用户余额是否已退款")
        print(f"   4. 验证回调记录表中是否有对应的记录")

if __name__ == "__main__":
    main()

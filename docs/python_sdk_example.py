#!/usr/bin/env python3
"""
Go-Kuaidi 开放平台 Python SDK 示例

使用说明：
1. 安装依赖：pip install requests
2. 替换 client_id 和 client_secret
3. 运行示例：python python_sdk_example.py

版本：v1.0.0
更新时间：2025-06-29
"""

import requests
import time
import json
import hmac
import hashlib
import base64
import urllib.parse
from typing import Dict, Any, Optional


class GoKuaidiClient:
    """Go-Kuaidi 开放平台客户端"""
    
    def __init__(self, client_id: str, client_secret: str, base_url: str = "http://api.go-kuaidi.com"):
        """
        初始化客户端
        
        Args:
            client_id: 客户端ID
            client_secret: 客户端密钥
            base_url: API基础地址
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'GoKuaidi-Python-SDK/1.0.0'
        })
    
    def query_price(self, from_province: str, from_city: str, to_province: str, to_city: str, 
                   weight: float, length: Optional[float] = None, width: Optional[float] = None, 
                   height: Optional[float] = None) -> Dict[str, Any]:
        """
        查询快递价格
        
        Args:
            from_province: 发件省份
            from_city: 发件城市
            to_province: 收件省份
            to_city: 收件城市
            weight: 重量（公斤）
            length: 长度（厘米，可选）
            width: 宽度（厘米，可选）
            height: 高度（厘米，可选）
            
        Returns:
            API响应结果
        """
        timestamp = str(int(time.time()))
        
        # 构建业务参数
        business_params = {
            "from_city": from_city,
            "from_province": from_province,
            "to_city": to_city,
            "to_province": to_province,
            "weight": weight
        }
        
        # 添加可选的体积参数
        if length is not None:
            business_params["length"] = length
        if width is not None:
            business_params["width"] = width
        if height is not None:
            business_params["height"] = height
        
        # 构建请求体（注意：字段必须按字典序排序）
        request_body = {
            "apiMethod": "QUERY_PRICE",
            "businessParams": business_params,
            "clientType": "api",
            "timestamp": timestamp,
            "username": self.client_id
        }
        
        # 序列化请求体（用于签名计算）
        body_json = json.dumps(request_body, ensure_ascii=False, separators=(',', ':'))
        
        # 计算签名
        signature = self._calculate_signature(timestamp, "/api/gateway/execute", body_json)
        
        # 添加签名到请求体
        request_body["sign"] = signature
        
        # 发送请求
        try:
            response = self.session.post(
                f"{self.base_url}/api/gateway/execute",
                json=request_body,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise GoKuaidiAPIException(f"API请求失败: {str(e)}")
    
    def _calculate_signature(self, timestamp: str, path: str, request_body: str) -> str:
        """
        计算HMAC-SHA256签名
        
        Args:
            timestamp: 时间戳
            path: API路径
            request_body: 请求体JSON字符串
            
        Returns:
            Base64编码的签名
        """
        # 1. 构建签名参数
        params = {
            "client_id": self.client_id,
            "nonce": timestamp,  # 统一网关使用timestamp作为nonce
            "path": path,
            "timestamp": timestamp
        }
        
        # 2. 参数排序和URL编码
        param_parts = []
        for key in sorted(params.keys()):
            encoded_value = urllib.parse.quote(str(params[key]), safe='')
            param_parts.append(f"{key}={encoded_value}")
        
        string_to_sign = "&".join(param_parts)
        
        # 3. 添加请求体
        if request_body:
            body_encoded = base64.b64encode(request_body.encode('utf-8')).decode('utf-8')
            string_to_sign += f"&body={body_encoded}"
        
        # 4. HMAC-SHA256计算
        signature_bytes = hmac.new(
            self.client_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        # 5. Base64编码
        return base64.b64encode(signature_bytes).decode('utf-8')


class GoKuaidiAPIException(Exception):
    """Go-Kuaidi API异常"""
    pass


def main():
    """示例用法"""
    
    # 初始化客户端（请替换为您的真实凭证）
    client = GoKuaidiClient(
        client_id="your_client_id",
        client_secret="your_client_secret",
        base_url="http://localhost:8081"  # 测试环境
    )
    
    try:
        # 查询价格
        print("🚀 开始查询快递价格...")
        result = client.query_price(
            from_province="广东省",
            from_city="深圳市",
            to_province="北京市",
            to_city="北京市",
            weight=2.5
        )
        
        # 处理结果
        if result.get("success"):
            prices = result["data"]["data"]
            print(f"✅ 查询成功！共找到 {len(prices)} 个价格选项：\n")
            
            # 按价格排序
            prices.sort(key=lambda x: x["price"])
            
            # 显示前5个最便宜的选项
            for i, price in enumerate(prices[:5], 1):
                print(f"{i}. {price['express_name']} - {price['product_name']}")
                print(f"   💰 价格：¥{price['price']}")
                print(f"   ⚖️  计费重量：{price['calc_weight']}kg")
                print(f"   🚚 预计送达：{price['delivery_time']}")
                print(f"   📦 续重单价：¥{price['continued_weight_per_kg']}/kg")
                print()
        else:
            print(f"❌ 查询失败：{result.get('msg', '未知错误')}")
            
    except GoKuaidiAPIException as e:
        print(f"❌ API异常：{e}")
    except Exception as e:
        print(f"❌ 未知错误：{e}")


if __name__ == "__main__":
    main()

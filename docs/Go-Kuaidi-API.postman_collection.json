{"info": {"name": "Go-Kuaidi API", "description": "Go-Kuaidi 开放平台API接口集合\n\n使用说明：\n1. 设置环境变量 client_id 和 client_secret\n2. client_id 用作 username 参数\n3. client_secret 仅用于签名计算，不传输", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://api.go-kuaidi.com", "type": "string"}, {"key": "client_id", "value": "FZQjP324wl5GrP9r7UYRS91Z", "type": "string", "description": "客户端ID，用作username参数"}, {"key": "client_secret", "value": "VdgGgIn8pERM9mHVqvDtm7RNm7jvZZu6", "type": "string", "description": "客户端密钥，仅用于签名计算"}], "item": [{"name": "价格查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/gateway/execute", "host": ["{{base_url}}"], "path": ["api", "gateway", "execute"]}, "body": {"mode": "raw", "raw": "{\n  \"clientType\": \"api\",\n  \"username\": \"{{client_id}}\",\n  \"timestamp\": \"{{$timestamp}}\",\n  \"sign\": \"需要计算签名\",\n  \"apiMethod\": \"QUERY_PRICE\",\n  \"businessParams\": {\n    \"from_province\": \"广东省\",\n    \"from_city\": \"深圳市\",\n    \"to_province\": \"北京市\",\n    \"to_city\": \"北京市\",\n    \"weight\": 2.5\n  }\n}"}, "description": "查询快递价格接口\n\n**注意**：需要先计算签名后再发送请求"}, "response": [{"name": "成功响应示例", "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 200,\n  \"msg\": \"操作成功\",\n  \"data\": {\n    \"success\": true,\n    \"data\": [\n      {\n        \"express_code\": \"STO\",\n        \"express_name\": \"申通快递\",\n        \"product_name\": \"标准快递\",\n        \"price\": 9.6,\n        \"continued_weight_per_kg\": 2.3,\n        \"calc_weight\": 3,\n        \"order_code\": \"ENHANCED_ORDER_CODE_...\"\n      }\n    ]\n  },\n  \"success\": true\n}"}, {"name": "签名验证失败", "status": "Unauthorized", "code": 401, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 401,\n  \"msg\": \"签名验证失败\",\n  \"success\": false\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 签名计算脚本", "const CryptoJS = require('crypto-js');", "", "// 获取环境变量", "const clientId = pm.variables.get('client_id');", "const clientSecret = pm.variables.get('client_secret');", "const timestamp = Math.floor(Date.now() / 1000).toString();", "", "// 构建请求体", "const requestBody = {", "  'apiMethod': 'QUERY_PRICE',", "  'businessParams': {", "    'from_city': '深圳市',", "    'from_province': '广东省',", "    'to_city': '北京市',", "    'to_province': '北京市',", "    'weight': 2.5", "  },", "  'clientType': 'api',", "  'timestamp': timestamp,", "  'username': clientId", "};", "", "// 序列化请求体", "const bodyJson = JSON.stringify(requestBody);", "", "// 构建签名参数", "const path = '/api/gateway/execute';", "const encodedPath = encodeURIComponent(path);", "const params = `client_id=${clientId}&nonce=${timestamp}&path=${encodedPath}&timestamp=${timestamp}`;", "", "// 添加请求体", "const bodyBase64 = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(bodyJson));", "const stringToSign = `${params}&body=${bodyBase64}`;", "", "// 计算HMAC-SHA256签名", "const signature = CryptoJS.enc.Base64.stringify(CryptoJS.HmacSHA256(stringToSign, clientSecret));", "", "// 设置变量", "pm.variables.set('timestamp', timestamp);", "pm.variables.set('signature', signature);", "", "console.log('Timestamp:', timestamp);", "console.log('String to sign:', stringToSign);", "console.log('Signature:', signature);"]}}]}
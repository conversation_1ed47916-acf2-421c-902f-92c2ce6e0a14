#!/bin/bash

# 设置脚本在出错时退出
set -e

# 配置变量
# 清除代理设置，避免影响数据库连接
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY all_proxy ALL_PROXY

export DATABASE_URL="*************************************************/go_kuaidi"
export SERVER_PORT="8081"
export GIN_MODE="debug"

# 日志目录设置
LOG_DIR="logs"
LOG_FILE="$LOG_DIR/go-kuaidi-local-$(date +%Y%m%d_%H%M%S).log"

# 二进制文件名
BINARY_NAME="go-kuaidi-local"

echo "🚀 启动 Go-Kuaidi 本地开发服务器..."
echo "📍 访问地址: http://localhost:$SERVER_PORT"

# 需求1: 自动终止旧端口8081的进程
echo "🔍 检查端口 $SERVER_PORT 是否被占用..."
OLD_PID=$(lsof -ti:$SERVER_PORT 2>/dev/null || true)

if [ ! -z "$OLD_PID" ]; then
    echo "⚠️  发现端口 $SERVER_PORT 被进程 $OLD_PID 占用"
    echo "🔪 正在终止旧进程..."
    
    # 尝试优雅终止
    kill -TERM $OLD_PID 2>/dev/null || true
    sleep 2
    
    # 检查进程是否还在运行
    if kill -0 $OLD_PID 2>/dev/null; then
        echo "⚡ 强制终止进程 $OLD_PID"
        kill -KILL $OLD_PID 2>/dev/null || true
        sleep 1
    fi
    
    echo "✅ 旧进程已终止"
else
    echo "✅ 端口 $SERVER_PORT 空闲，可以启动"
fi

# 需求2: 设置双日志 - 创建日志目录
if [ ! -d "$LOG_DIR" ]; then
    echo "📁 创建日志目录: $LOG_DIR"
    mkdir -p "$LOG_DIR"
fi

echo "📝 日志文件: $LOG_FILE"
echo "📺 控制台日志: 实时显示"
echo ""
echo "▶️  启动服务..."
echo "🛑 按 Ctrl+C 停止服务"
echo "----------------------------------------"

# 需求2: 双日志实现 - 控制台显示 + 文件保存
# 使用 tee 命令同时输出到控制台和文件
# 使用 exec 重定向所有输出
exec > >(tee -a "$LOG_FILE")
exec 2>&1

# 在日志文件中记录启动信息
echo "[$(date '+%Y-%m-%d %H:%M:%S')] ==============================================="
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Go-Kuaidi 本地服务启动"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口: $SERVER_PORT"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 模式: $GIN_MODE"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 日志文件: $LOG_FILE"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] ==============================================="

# 设置清理函数，在脚本退出时执行
cleanup() {
    echo ""
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 🛑 接收到停止信号，正在关闭服务..."
    
    # 清理编译生成的二进制文件
    if [ -f "$BINARY_NAME" ]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 🗑️  删除编译文件: $BINARY_NAME"
        rm -f "$BINARY_NAME"
    fi
    
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 📝 日志已保存到: $LOG_FILE"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 👋 服务已停止"
    exit 0
}

# 注册信号处理器
trap cleanup SIGINT SIGTERM

# 编译和启动Go应用程序
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 🔨 正在编译 Go 应用程序..."

# 编译二进制文件
if go build -o "$BINARY_NAME" cmd/main.go; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ✅ 编译成功: $BINARY_NAME"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 🚀 正在启动应用程序..."
    
    # 启动编译后的二进制文件
    ./"$BINARY_NAME"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ❌ 编译失败"
    exit 1
fi

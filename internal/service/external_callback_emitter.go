package service

import (
	"context"

	"github.com/your-org/go-kuaidi/internal/model"
)

// ExternalCallbackEmitter 管理员同步后对外补发统一回调的能力抽象
// 由回调子系统提供实现，避免 service 与 callback 包循环依赖
// 最小职责：生成统一回调记录并触发外部转发
// 注意：实现方应确保幂等与重试由自身体系处理

type ExternalCallbackEmitter interface {
	// EmitAdminSyncStatusUpdate 在管理员状态同步成功后补发“订单状态变更”回调
	EmitAdminSyncStatusUpdate(ctx context.Context, order *model.OrderRecord, oldStatus, newStatus model.SystemOrderStatus) error

	// EmitAdminSyncBillingUpdate 在管理员同步后补发“计费更新”回调（包含重量信息）
	// 参数：oldFee 为变更前订单actual_fee，newFee 为供应商总费用（处理差额依据）
	EmitAdminSyncBillingUpdate(ctx context.Context, order *model.OrderRecord, oldFee, newFee float64, weightInfo *model.ShippingWeightInfo) error
}

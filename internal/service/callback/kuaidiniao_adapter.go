package callback

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// KuaidiNiaoCallbackAdapter 快递鸟回调适配器
type KuaidiNiaoCallbackAdapter struct {
	apiKey string // 用于签名验证
}

// NewKuaidiNiaoCallbackAdapter 创建快递鸟回调适配器
func NewKuaidiNiaoCallbackAdapter(apiKey string) ProviderCallbackAdapter {
	return &KuaidiNiaoCallbackAdapter{
		apiKey: apiKey,
	}
}

// ValidateSignature 验证快递鸟回调签名
func (a *KuaidiNiaoCallbackAdapter) ValidateSignature(rawData []byte, headers map[string]string) error {
	// 🔥 快递鸟回调不需要签名验证
	// 快递鸟推送回调是第三方主动发送，不使用我们的签名体系
	// 已在签名中间件层面跳过验证，这里直接返回成功
	return nil
}

// ParseCallback 解析快递鸟回调数据
func (a *KuaidiNiaoCallbackAdapter) ParseCallback(rawData []byte) (*model.ParsedCallbackData, error) {
	// 🔥 修复：快递鸟回调数据是URL编码的表单数据，需要先解析表单
	dataStr := string(rawData)

	// 检查是否是表单数据格式
	var callbackData map[string]interface{}
	var err error

	if strings.Contains(dataStr, "RequestData=") {
		// URL编码的表单数据格式（快递鸟官方格式）
		callbackData, err = a.parseFormData(dataStr)
		if err != nil {
			return nil, fmt.Errorf("解析快递鸟表单数据失败: %w", err)
		}
	} else {
		// JSON格式（测试数据或其他格式）
		if err := json.Unmarshal(rawData, &callbackData); err != nil {
			return nil, fmt.Errorf("解析快递鸟JSON数据失败: %w", err)
		}
	}

	// 🔥 企业级修复：智能判断快递鸟事件类型
	// 优先根据状态码和费用信息判断，而不是仅依赖RequestType
	var eventType string

	// 先检查Data数组中的状态码和费用信息
	var hasFeeBillingInfo bool
	var stateCode string
	if dataArray, ok := callbackData["Data"].([]interface{}); ok && len(dataArray) > 0 {
		if orderData, ok := dataArray[0].(map[string]interface{}); ok {
			stateCode, _ = orderData["State"].(string)

			// 检查是否包含费用信息
			if totalFee, exists := orderData["TotalFee"]; exists && totalFee != nil {
				if fee, ok := totalFee.(float64); ok && fee > 0 {
					hasFeeBillingInfo = true
				}
			}
			if !hasFeeBillingInfo {
				if cost, exists := orderData["Cost"]; exists && cost != nil {
					if costVal, ok := cost.(float64); ok && costVal > 0 {
						hasFeeBillingInfo = true
					}
				}
			}
		}
	}

	// 🔥 智能事件类型判断
	if hasFeeBillingInfo && (stateCode == "301" || stateCode == "208" || stateCode == "601") {
		// 状态码301=已揽件，208=重量修正，601=费用推送（申通）- 都包含费用信息
		eventType = model.EventTypeBillingUpdated
	} else if requestType, ok := callbackData["RequestType"].(string); ok {
		eventType = a.mapRequestTypeToEventType(requestType)
	} else if pushType, ok := callbackData["PushType"].(float64); ok {
		eventType = a.mapPushTypeToEventType(int(pushType))
	} else {
		return nil, fmt.Errorf("缺少推送类型(RequestType或PushType)")
	}

	// 从Data数组中提取订单信息
	var orderNo, customerOrderNo, trackingNo, userID string
	if dataArray, ok := callbackData["Data"].([]interface{}); ok && len(dataArray) > 0 {
		if orderData, ok := dataArray[0].(map[string]interface{}); ok {
			orderNo, _ = orderData["OrderCode"].(string)
			customerOrderNo, _ = orderData["CustomerOrderNo"].(string)
			trackingNo, _ = orderData["LogisticCode"].(string)
		}
	}

	// 提取用户ID
	if ebusinessID, ok := callbackData["EBusinessID"].(string); ok {
		userID = ebusinessID
	}

	// 🔥 修复：直接传递Data数组中的第一个元素，而不是整个callbackData
	var orderData interface{}
	if dataArray, ok := callbackData["Data"].([]interface{}); ok && len(dataArray) > 0 {
		orderData = dataArray[0]
	} else {
		orderData = callbackData
	}

	// 构建解析后的回调数据
	parsedData := &model.ParsedCallbackData{
		Type:            eventType,
		OrderNo:         orderNo,
		CustomerOrderNo: customerOrderNo,
		TrackingNo:      trackingNo,
		UserID:          userID,
		Data:            orderData, // 直接传递订单数据，而不是整个回调数据
		Timestamp:       util.NowBeijing(),
	}

	return parsedData, nil
}

// parseFormData 解析快递鸟URL编码的表单数据
func (a *KuaidiNiaoCallbackAdapter) parseFormData(dataStr string) (map[string]interface{}, error) {
	// 解析URL编码的表单数据
	values, err := url.ParseQuery(dataStr)
	if err != nil {
		return nil, fmt.Errorf("解析URL编码数据失败: %w", err)
	}

	// 提取RequestData字段
	requestData := values.Get("RequestData")
	if requestData == "" {
		return nil, fmt.Errorf("缺少RequestData字段")
	}

	// 解析RequestData中的JSON数据
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(requestData), &jsonData); err != nil {
		return nil, fmt.Errorf("解析RequestData JSON失败: %w", err)
	}

	// 添加其他表单字段
	jsonData["RequestType"] = values.Get("RequestType")
	jsonData["DataSign"] = values.Get("DataSign")

	return jsonData, nil
}

// mapRequestTypeToEventType 将快递鸟RequestType映射为事件类型
func (a *KuaidiNiaoCallbackAdapter) mapRequestTypeToEventType(requestType string) string {
	switch requestType {
	case "103": // 物流轨迹推送
		return model.EventTypeOrderStatusChanged
	case "104": // 计费推送
		return model.EventTypeBillingUpdated
	case "1801": // 下单结果推送
		return "order_created"
	default:
		return model.EventTypeOrderStatusChanged // 默认为状态变更
	}
}

// StandardizeData 标准化快递鸟回调数据
func (a *KuaidiNiaoCallbackAdapter) StandardizeData(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	switch data.Type {
	case model.EventTypeOrderStatusChanged:
		return a.standardizeStatusUpdate(data)
	case model.EventTypeBillingUpdated:
		return a.standardizeBillingUpdate(data)
	case "pickup_updated":
		return a.standardizePickupUpdate(data)
	case "workorder_updated":
		return a.standardizeWorkOrderUpdate(data)
	case "workorder_compensation":
		return a.standardizeWorkOrderCompensation(data)
	default:
		return nil, fmt.Errorf("不支持的事件类型: %s", data.Type)
	}
}

// BuildResponse 构建快递鸟回调响应 - 严格按照官方文档5.18格式
func (a *KuaidiNiaoCallbackAdapter) BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse {
	// 🔥 企业级修复：严格按照快递鸟官方文档5.18要求的响应格式
	// 官方要求的响应格式：
	// {
	//     "EBusinessID": "350238",
	//     "UpdateTime": "2023-02-08 16:54:59",
	//     "Success": true,
	//     "Reason": "成功"
	// }

	// 获取当前北京时间
	updateTime := time.Now().In(time.FixedZone("CST", 8*3600)).Format("2006-01-02 15:04:05")

	return &model.CallbackResponse{
		Success:      true,
		Code:         "200",
		Message:      "成功",
		DirectReturn: true, // 快递鸟需要直接返回成功标识
		Data: map[string]interface{}{
			"EBusinessID": "1778716",  // 快递鸟用户ID（必需）
			"UpdateTime":  updateTime, // 处理完结时间（必需）
			"Success":     true,       // 成功与否（必需）
			"Reason":      "成功",       // 描述（必需）
		},
	}
}

// mapPushTypeToEventType 映射快递鸟推送类型到事件类型
func (a *KuaidiNiaoCallbackAdapter) mapPushTypeToEventType(pushType int) string {
	switch pushType {
	case 1: // 状态推送
		return model.EventTypeOrderStatusChanged
	case 2: // 计费推送
		return model.EventTypeBillingUpdated
	case 3: // 揽收推送
		return "pickup_updated"
	case 4: // 异常推送
		return model.EventTypeOrderStatusChanged // 归类为状态变更
	case 5: // 订单变更推送
		return model.EventTypeOrderStatusChanged
	case 401: // 推送工单处理结果
		return "workorder_updated"
	case 501: // 推送工单赔付结果
		return "workorder_compensation"
	default:
		return model.EventTypeOrderStatusChanged // 默认归类为状态变更
	}
}

// standardizeStatusUpdate 标准化状态更新事件
func (a *KuaidiNiaoCallbackAdapter) standardizeStatusUpdate(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 🔍 调试：验证方法是否被调用
	fmt.Printf("🔍 standardizeStatusUpdate 被调用，订单号: %s\n", data.OrderNo)

	// 🔥 修复：现在data.Data已经是订单数据了，直接使用
	rawData, ok := data.Data.(map[string]interface{})
	if !ok {
		fmt.Printf("🔍 数据类型转换失败: %T\n", data.Data)
		return nil, fmt.Errorf("无效的订单数据类型: %T", data.Data)
	}

	fmt.Printf("🔍 rawData 转换成功，开始处理快递员信息\n")

	// 提取状态信息
	state, _ := rawData["State"].(string)
	stateEx, _ := rawData["StateEx"].(string)
	stateDesc := a.getStateDescription(state, stateEx)

	// 映射状态
	standardStatus := a.mapState(state, stateEx)

	// 提取时间
	eventTime := data.Timestamp
	if timeStr, ok := rawData["PushTime"].(string); ok {
		if t, err := a.parseTime(timeStr); err == nil {
			eventTime = t
		}
	}

	// 构建状态更新数据
	statusData := &model.OrderStatusChangedData{
		NewStatus:  standardStatus,
		StatusDesc: stateDesc,
		UpdateTime: eventTime,
	}

	// 🔥 修复：正确处理快递鸟的PickerInfo数组格式
	if pickerInfoArray, ok := rawData["PickerInfo"].([]interface{}); ok && len(pickerInfoArray) > 0 {
		if pickerInfo, ok := pickerInfoArray[0].(map[string]interface{}); ok {
			// 提取快递员信息
			if personName, ok := pickerInfo["PersonName"].(string); ok && personName != "" {
				statusData.CourierInfo = &model.CourierInfo{
					Name: personName,
				}

				// 提取快递员电话
				if personTel, ok := pickerInfo["PersonTel"].(string); ok && personTel != "" {
					statusData.CourierInfo.Phone = personTel
				}

				// 提取取件码
				if pickupCode, ok := pickerInfo["PickupCode"].(string); ok && pickupCode != "" {
					statusData.CourierInfo.PickupCode = pickupCode
				}

				// 提取快递员编号
				if personCode, ok := pickerInfo["PersonCode"].(string); ok && personCode != "" {
					statusData.CourierInfo.Code = personCode
				}
			}
		}
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         data.OrderNo,
		PlatformOrderNo: data.CustomerOrderNo,
		CustomerOrderNo: data.CustomerOrderNo,
		TrackingNo:      data.TrackingNo,
		Provider:        "kuaidiniao",
		Timestamp:       eventTime,
		Data:            statusData,
	}, nil
}

// standardizeBillingUpdate 标准化计费更新事件
// 🔥 企业级修复：完整提取快递鸟费用信息，支持费用差额处理
func (a *KuaidiNiaoCallbackAdapter) standardizeBillingUpdate(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	rawData := data.Data.(map[string]interface{})

	// 🔥 关键修复：提取完整的快递鸟费用信息
	weight, _ := rawData["Weight"].(float64)
	cost, _ := rawData["Cost"].(float64)
	totalFee, _ := rawData["TotalFee"].(float64)
	actualWeight, _ := rawData["ActualWeight"].(float64)
	volumeWeight, _ := rawData["VolumeWeight"].(float64)
	volume, _ := rawData["Volume"].(float64)

	// 提取费用明细
	firstWeightAmount, _ := rawData["FirstWeightAmount"].(float64)
	continuousWeightAmount, _ := rawData["ContinuousWeightAmount"].(float64)
	insureAmount, _ := rawData["InsureAmount"].(float64)
	packageFee, _ := rawData["PackageFee"].(float64)
	overFee, _ := rawData["OverFee"].(float64)
	otherFee, _ := rawData["OtherFee"].(float64)
	otherFeeDetail, _ := rawData["OtherFeeDetail"].(string)

	// 🔥 关键：使用TotalFee作为主要费用，如果没有则使用Cost
	finalTotalFee := totalFee
	if finalTotalFee <= 0 {
		finalTotalFee = cost
	}

	// 提取时间
	eventTime := data.Timestamp
	if timeStr, ok := rawData["PushTime"].(string); ok {
		if t, err := a.parseTime(timeStr); err == nil {
			eventTime = t
		}
	}

	// 🔥 企业级费用明细构建
	var feeDetails []model.FeeDetail

	// 基础运费
	if cost > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "freight",
			FeeDesc: "基础运费",
			Amount:  cost,
		})
	}

	// 首重费用
	if firstWeightAmount > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "first_weight",
			FeeDesc: "首重费用",
			Amount:  firstWeightAmount,
		})
	}

	// 续重费用
	if continuousWeightAmount > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "additional_weight",
			FeeDesc: "续重费用",
			Amount:  continuousWeightAmount,
		})
	}

	// 保价费
	if insureAmount > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "insurance",
			FeeDesc: "保价费",
			Amount:  insureAmount,
		})
	}

	// 包装费
	if packageFee > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "packaging",
			FeeDesc: "包装费",
			Amount:  packageFee,
		})
	}

	// 超长超重费
	if overFee > 0 {
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "oversize",
			FeeDesc: "超长超重费",
			Amount:  overFee,
		})
	}

	// 其他费用
	if otherFee > 0 {
		feeDesc := "其他费用"
		if otherFeeDetail != "" {
			feeDesc = fmt.Sprintf("其他费用(%s)", otherFeeDetail)
		}
		feeDetails = append(feeDetails, model.FeeDetail{
			FeeType: "other",
			FeeDesc: feeDesc,
			Amount:  otherFee,
		})
	}

	// 🔥 计算计费重量：取实际重量和体积重量的较大值
	chargedWeight := weight
	if actualWeight > 0 {
		chargedWeight = actualWeight
	}
	if volumeWeight > chargedWeight {
		chargedWeight = volumeWeight
	}

	// 🔥 构建完整的计费数据（符合model.BillingUpdatedData结构）
	billingData := &model.BillingUpdatedData{
		Weight:        weight,
		ChargedWeight: chargedWeight, // 🔥 使用计费重量字段
		Volume:        volume,
		Cost:          cost,
		TotalFee:      finalTotalFee, // 🔥 关键：使用完整的总费用
		UpdateTime:    eventTime,
		FeeDetails:    feeDetails,
		// 🔥 使用PricingInfo存储快递鸟特有的费用信息
		PricingInfo: &model.PricingInfo{
			FirstWeight:   1.0, // 快递鸟通常首重1kg
			FirstPrice:    firstWeightAmount,
			ContinuePrice: continuousWeightAmount,
			Discount:      0.0,
			DiscountFee:   0.0,
		},
	}

	return &model.StandardizedCallbackData{
		EventType:       model.EventTypeBillingUpdated,
		OrderNo:         data.OrderNo,
		PlatformOrderNo: data.CustomerOrderNo,
		CustomerOrderNo: data.CustomerOrderNo,
		TrackingNo:      data.TrackingNo,
		Provider:        "kuaidiniao",
		Timestamp:       eventTime,
		Data:            billingData,
	}, nil
}

// standardizePickupUpdate 标准化揽收更新事件
func (a *KuaidiNiaoCallbackAdapter) standardizePickupUpdate(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	rawData := data.Data.(map[string]interface{})

	// 提取时间
	eventTime := data.Timestamp
	if timeStr, ok := rawData["PushTime"].(string); ok {
		if t, err := a.parseTime(timeStr); err == nil {
			eventTime = t
		}
	}

	// 构建揽收数据
	pickupData := &model.PickupInfoUpdatedData{
		UpdateTime: eventTime,
	}

	// 提取快递员信息
	if courierName, ok := rawData["CourierName"].(string); ok && courierName != "" {
		pickupData.CourierInfo = &model.CourierInfo{
			Name: courierName,
		}
		if courierPhone, ok := rawData["CourierPhone"].(string); ok {
			pickupData.CourierInfo.Phone = courierPhone
		}
		if courierCode, ok := rawData["CourierCode"].(string); ok {
			pickupData.CourierInfo.Code = courierCode
		}
		if stationName, ok := rawData["StationName"].(string); ok {
			pickupData.CourierInfo.Station = stationName
		}
	}

	// 提取取件码
	if pickupCode, ok := rawData["PickupCode"].(string); ok {
		pickupData.PickupCode = pickupCode
	}

	return &model.StandardizedCallbackData{
		EventType:       "pickup_updated",
		OrderNo:         data.OrderNo,
		PlatformOrderNo: data.CustomerOrderNo,
		CustomerOrderNo: data.CustomerOrderNo,
		TrackingNo:      data.TrackingNo,
		Provider:        "kuaidiniao",
		Timestamp:       eventTime,
		Data:            pickupData,
	}, nil
}

// mapState 映射快递鸟状态到标准状态
func (a *KuaidiNiaoCallbackAdapter) mapState(state, stateEx string) string {
	// 🔥 企业级状态映射：支持完整的快递鸟状态码

	// 优先使用详细状态StateEx
	if stateEx != "" {
		switch stateEx {
		case "1":
			return "picked_up" // 已揽收
		case "2":
			return "in_transit" // 在途中
		case "3":
			return "delivered" // 已签收
		case "201":
			return "in_transit" // 到达派件城市
		case "202":
			return "out_for_delivery" // 派件中
		case "204":
			return "in_transit" // 到达中转站
		case "301":
			return "exception" // 异常
		case "302":
			return "delivered" // 正常签收
		case "304":
			return "delivered_abnormal" // 拒收
		case "311":
			return "exception" // 丢失
		case "412":
			return "awaiting_pickup" // 代收点暂存
		}
	}

	// 使用基础状态State（数字状态码）
	switch state {
	// === 下单状态 ===
	case "100":
		return "submitted" // 下单成功
	case "101":
		return "submit_failed" // 下单失败
	case "102":
		return "submitted" // 已分配网点（仍为已提交状态）
	case "103":
		return "assigned" // 已分配快递员
	case "104":
		return "picked_up" // 已取件
	case "105":
		return "in_transit" // 运输中
	case "106":
		return "out_for_delivery" // 派送中
	case "107":
		return "delivered" // 已签收
	case "108":
		return "exception" // 异常

	// === 调度和取消状态 ===
	case "99":
		return "pickup_failed" // 🔥 修复：调度失败（快递鸟官方文档：接口指令103，状态码99）
	case "203":
		return "cancelled" // 订单已取消
	case "204":
		return "pickup_failed" // 揽收失败
	case "205":
		return "voided" // 作废
	case "209":
		return "cancelled" // 其他取消原因

	// === 计费状态 ===
	case "301":
		return "billed" // 已计费
	case "302":
		return "weight_updated" // 重量更新

	// === 异常状态 ===
	case "401":
		return "exception" // 异常
	case "402":
		return "returned" // 已退回
	case "403":
		return "forwarded" // 已转寄

	// === 物流轨迹状态（字符串状态码）===
	case "0":
		return "submitted" // 暂无物流信息
	case "1":
		return "picked_up" // 已揽收
	case "2":
		return "in_transit" // 在途中
	case "3":
		return "delivered" // 已签收
	case "4":
		return "exception" // 异常

	default:
		return "submitted" // 默认状态（避免unknown状态）
	}
}

// getStateDescription 获取状态描述
func (a *KuaidiNiaoCallbackAdapter) getStateDescription(state, stateEx string) string {
	statusMap := map[string]string{
		"0":   "暂无物流信息",
		"1":   "已揽收",
		"2":   "在途中",
		"3":   "已签收",
		"4":   "异常",
		"99":  "调度失败", // 🔥 修复：添加调度失败状态描述
		"100": "下单成功",
		"101": "下单失败",
		"102": "已分配网点",
		"103": "已分配快递员",
		"104": "已取件",
		"105": "运输中",
		"106": "派送中",
		"107": "已签收",
		"108": "异常",
		"203": "订单已取消",
		"204": "揽收失败",
		"205": "已作废",
		"209": "其他取消原因",
	}

	if desc, exists := statusMap[state]; exists {
		return desc
	}

	return "状态未知"
}

// parseTime 解析时间字符串
func (a *KuaidiNiaoCallbackAdapter) parseTime(timeStr string) (time.Time, error) {
	// 常见时间格式列表
	formats := []string{
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"Mon Jan 02 15:04:05 MST 2006", // Thu Mar 30 05:37:57 CST 2023
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			// 转换为北京时间
			return util.ToBeijing(t), nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// standardizeWorkOrderUpdate 标准化工单处理结果更新事件
func (a *KuaidiNiaoCallbackAdapter) standardizeWorkOrderUpdate(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 解析工单处理结果数据
	rawData, ok := data.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的工单数据类型: %T", data.Data)
	}

	// 提取工单信息
	ticketNumber, _ := rawData["TicketNumber"].(string)
	kdnOrderCode, _ := rawData["KdnOrderCode"].(string)
	dealResult, _ := rawData["DealResult"].(string)
	status, _ := rawData["Status"].(float64)
	dealResultFiles, _ := rawData["DealResultFiles"].(string)

	// 构建工单更新数据
	workOrderData := map[string]interface{}{
		"ticket_number":     ticketNumber,
		"kdn_order_code":    kdnOrderCode,
		"deal_result":       dealResult,
		"status":            int(status),
		"status_name":       a.mapWorkOrderStatus(int(status)),
		"deal_result_files": dealResultFiles,
		"provider":          "kuaidiniao",
		"update_time":       util.NowBeijing(),
	}

	// 构建标准化数据
	standardized := &model.StandardizedCallbackData{
		EventType:       "workorder_updated",
		OrderNo:         kdnOrderCode,
		CustomerOrderNo: data.CustomerOrderNo,
		TrackingNo:      data.TrackingNo,
		Provider:        "kuaidiniao",
		Timestamp:       util.NowBeijing(),
		Data:            workOrderData,
	}

	return standardized, nil
}

// standardizeWorkOrderCompensation 标准化工单赔付结果事件
func (a *KuaidiNiaoCallbackAdapter) standardizeWorkOrderCompensation(data *model.ParsedCallbackData) (*model.StandardizedCallbackData, error) {
	// 解析工单赔付数据
	rawData, ok := data.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无效的工单赔付数据类型: %T", data.Data)
	}

	// 提取赔付信息
	ticketNumber, _ := rawData["TicketNumber"].(string)
	kdnOrderCode, _ := rawData["KdnOrderCode"].(string)
	compensationAmount, _ := rawData["CompensationAmount"].(float64)
	compensationReason, _ := rawData["CompensationReason"].(string)
	status, _ := rawData["Status"].(float64)

	// 构建工单赔付数据
	compensationData := map[string]interface{}{
		"ticket_number":       ticketNumber,
		"kdn_order_code":      kdnOrderCode,
		"compensation_amount": compensationAmount,
		"compensation_reason": compensationReason,
		"status":              int(status),
		"status_name":         a.mapWorkOrderStatus(int(status)),
		"provider":            "kuaidiniao",
		"update_time":         util.NowBeijing(),
	}

	// 构建标准化数据
	standardized := &model.StandardizedCallbackData{
		EventType:       "workorder_compensation",
		OrderNo:         kdnOrderCode,
		CustomerOrderNo: data.CustomerOrderNo,
		TrackingNo:      data.TrackingNo,
		Provider:        "kuaidiniao",
		Timestamp:       util.NowBeijing(),
		Data:            compensationData,
	}

	return standardized, nil
}

// mapWorkOrderStatus 映射快递鸟工单状态到标准状态
func (a *KuaidiNiaoCallbackAdapter) mapWorkOrderStatus(status int) string {
	switch status {
	case 0:
		return "pending" // 待处理
	case 1:
		return "processing" // 处理中
	case 2:
		return "completed" // 已处理
	default:
		return "unknown" // 未知状态
	}
}

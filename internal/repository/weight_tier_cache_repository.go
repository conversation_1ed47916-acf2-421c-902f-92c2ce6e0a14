package repository

import (
	"context"
	"database/sql"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

const (
	// 重试配置
	maxRetryAttempts = 3
	baseRetryDelay   = 50  // 毫秒
	maxRetryDelay    = 500 // 毫秒
)

// WeightTierCacheRepository 重量档位缓存仓储接口
type WeightTierCacheRepository interface {
	// 缓存查询和存储
	GetCachedPrice(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weightKg int) (*model.WeightTierPriceCache, error)
	StoreCachedPrice(ctx context.Context, cache *model.WeightTierPriceCache) error
	UpdateCachedPrice(ctx context.Context, cache *model.WeightTierPriceCache) error
	InvalidateCache(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weightKg int) error
	InvalidateCacheByWeight(ctx context.Context, provider, expressCode string, weightKg int) error // 🔥 新增：按重量批量失效缓存

	// 缓存命中统计
	RecordCacheHit(ctx context.Context, cacheID string) error
	RecordCacheValidation(ctx context.Context, cacheID string) error

	// 价格验证记录
	SaveValidationRecord(ctx context.Context, validation *model.OrderPriceValidation) error
	GetValidationHistory(ctx context.Context, orderID string) ([]*model.OrderPriceValidation, error)

	// 统计查询
	GetCacheStatistics(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.WeightCacheStatistics, error)
	GetCacheOverview(ctx context.Context) ([]*model.WeightCacheOverview, error)
	GetValidationStats(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.PriceValidationStats, error)

	// 🚀 优化后的查询方法
	GetCacheOverviewOptimized(ctx context.Context, req *model.CacheOverviewRequest) (*model.CacheOverviewResponse, error)
	GetProviderGroupedOverviewOptimized(ctx context.Context) ([]*model.ProviderGroup, error)
	RefreshCacheViews(ctx context.Context) error

	// 新增：分组和详情查询
	GetProviderGroupedOverview(ctx context.Context) ([]*model.ProviderGroup, error)
	GetCacheDetails(ctx context.Context, req *model.CacheDetailRequest) ([]*model.RouteCacheInfo, int64, error)
	BatchInvalidateCache(ctx context.Context, provider, expressCode, route string) error

	// 路线管理
	GetActiveRoutes(ctx context.Context) ([]*model.RouteDefinition, error)
	GetRouteByProvinces(ctx context.Context, fromProvince, toProvince string) (*model.RouteDefinition, error)

	// 缓存维护
	CleanupInvalidCache(ctx context.Context) error
	GetCacheSize(ctx context.Context) (int64, error)

	// 🔥 新增：映射状态变更相关的缓存失效方法
	InvalidateCacheByPattern(ctx context.Context, provider, expressCode string) error
	ClearAllCache(ctx context.Context) error
	GetCacheStats(ctx context.Context) (map[string]interface{}, error)

	// 查询日志
	SavePriceQueryLog(ctx context.Context, logReq *model.WeightTierQueryLogRequest) error
	GetDailyCacheStats(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.DailyCacheStats, error)

	// 数据获取辅助方法
	GetExpressCodes(ctx context.Context, provider string) ([]string, error)
	GetProviders(ctx context.Context) ([]string, error)
	GetWeightTiers(ctx context.Context) ([]int, error)

	// 设置供应商状态检查器
	SetProviderStatusChecker(checker adapter.ProviderStatusChecker)
}

// weightTierCacheRepository 重量档位缓存仓储实现
type weightTierCacheRepository struct {
	db                    *sql.DB
	logger                *zap.Logger
	memoryCache           sync.Map // 内存缓存，提高查询性能
	stats                 *cacheStats
	providerStatusChecker adapter.ProviderStatusChecker // 供应商状态检查器
	keyLocks              sync.Map                      // 基于缓存键的细粒度锁
}

// cacheStats 缓存统计信息
type cacheStats struct {
	mu            sync.RWMutex
	hitCount      int64
	missCount     int64
	errorCount    int64
	retryCount    int64 // 重试次数统计
	conflictCount int64 // 并发冲突次数统计
	lastResetTime time.Time
}

// NewWeightTierCacheRepository 创建重量档位缓存仓储
func NewWeightTierCacheRepository(db *sql.DB, logger *zap.Logger) WeightTierCacheRepository {
	repo := &weightTierCacheRepository{
		db:     db,
		logger: logger,
		stats: &cacheStats{
			lastResetTime: util.NowBeijing(),
		},
	}

	// 初始化数据库连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	logger.Info("✅ 重量档位缓存仓储初始化成功",
		zap.Int("max_open_conns", 25),
		zap.Int("max_idle_conns", 5))

	return repo
}

// SetProviderStatusChecker 设置供应商状态检查器
func (r *weightTierCacheRepository) SetProviderStatusChecker(checker adapter.ProviderStatusChecker) {
	r.providerStatusChecker = checker
	r.logger.Info("供应商状态检查器已设置")
}

// GetCachedPrice 获取缓存价格
func (r *weightTierCacheRepository) GetCachedPrice(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weightKg int) (*model.WeightTierPriceCache, error) {
	// 0. 首先验证供应商状态
	if r.providerStatusChecker != nil {
		isActive, err := r.providerStatusChecker.IsProviderActive(ctx, provider)
		if err != nil {
			r.logger.Error("检查供应商状态失败",
				zap.String("provider", provider),
				zap.Error(err))
			r.incrementErrorCount()
			return nil, fmt.Errorf("检查供应商状态失败: %w", err)
		}
		if !isActive {
			r.logger.Debug("供应商已禁用，跳过缓存查询",
				zap.String("provider", provider))
			r.incrementMissCount()
			return nil, nil // 供应商已禁用，返回缓存未命中
		}
	}

	// 生成缓存键
	cacheKey := r.generateCacheKey(fromProvince, toProvince, provider, expressCode, weightKg)

	// 1. 先查内存缓存
	if cached, ok := r.memoryCache.Load(cacheKey); ok {
		if cache, ok := cached.(*model.WeightTierPriceCache); ok {
			// 检查缓存是否过期（24小时）
			if time.Since(cache.UpdatedAt) < 24*time.Hour && cache.IsValid {
				r.incrementHitCount()
				r.logger.Debug("内存缓存命中",
					zap.String("cache_key", cacheKey),
					zap.String("price", cache.Price.String()))
				return cache, nil
			} else {
				// 缓存过期，从内存中删除
				r.memoryCache.Delete(cacheKey)
			}
		}
	}

	// 2. 查询数据库
	cache, err := r.getCachedPriceFromDB(ctx, fromProvince, toProvince, provider, expressCode, weightKg)
	if err != nil {
		r.incrementErrorCount()
		return nil, err
	}

	if cache == nil {
		r.incrementMissCount()
		return nil, nil // 缓存未命中
	}

	// 3. 检查数据库缓存是否有效且未过期
	if !cache.IsValid || time.Since(cache.UpdatedAt) >= 24*time.Hour {
		r.incrementMissCount()
		return nil, nil // 缓存无效或过期
	}

	// 4. 将有效缓存加载到内存
	r.memoryCache.Store(cacheKey, cache)
	r.incrementHitCount()

	r.logger.Debug("数据库缓存命中",
		zap.String("cache_key", cacheKey),
		zap.String("price", cache.Price.String()),
		zap.Time("cached_at", cache.UpdatedAt))

	return cache, nil
}

// getCachedPriceFromDB 从数据库获取缓存价格
func (r *weightTierCacheRepository) getCachedPriceFromDB(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weightKg int) (*model.WeightTierPriceCache, error) {
	query := `
		SELECT id, from_province, to_province, provider, express_code, express_name, weight_kg,
			   price, continued_weight_per_kg, product_code, product_name, channel_id,
			   estimated_days, route_key, source, is_valid,
			   cache_hit_count, validation_count, last_hit_time, last_validated_time,
			   created_at, updated_at
		FROM weight_tier_price_cache
		WHERE from_province = $1 AND to_province = $2 AND provider = $3
		  AND express_code = $4 AND weight_kg = $5 AND is_valid = true
		ORDER BY updated_at DESC
		LIMIT 1
	`

	var cache model.WeightTierPriceCache
	var lastHitTime, lastValidatedTime sql.NullTime
	var productCode, productName, channelID sql.NullString

	err := r.db.QueryRowContext(ctx, query, fromProvince, toProvince, provider, expressCode, weightKg).Scan(
		&cache.ID, &cache.FromProvince, &cache.ToProvince, &cache.Provider, &cache.ExpressCode, &cache.ExpressName,
		&cache.WeightKg, &cache.Price, &cache.ContinuedWeightPerKg, &productCode, &productName, &channelID,
		&cache.EstimatedDays, &cache.RouteKey, &cache.Source, &cache.IsValid,
		&cache.CacheHitCount, &cache.ValidationCount, &lastHitTime, &lastValidatedTime,
		&cache.CreatedAt, &cache.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 缓存未命中
		}
		r.logger.Error("查询缓存价格失败",
			zap.Error(err),
			zap.String("route", fmt.Sprintf("%s->%s", fromProvince, toProvince)),
			zap.String("provider", provider),
			zap.String("express_code", expressCode),
			zap.Int("weight_kg", weightKg))
		return nil, fmt.Errorf("查询缓存价格失败: %w", err)
	}

	// 处理可空时间字段
	// 处理可空字段
	if lastHitTime.Valid {
		cache.LastHitTime = &lastHitTime.Time
	}
	if lastValidatedTime.Valid {
		cache.LastValidatedTime = &lastValidatedTime.Time
	}
	if productCode.Valid {
		cache.ProductCode = productCode.String
	}
	if productName.Valid {
		cache.ProductName = productName.String
	}
	if channelID.Valid {
		cache.ChannelID = channelID.String
	}

	return &cache, nil
}

// generateCacheKey 生成缓存键
func (r *weightTierCacheRepository) generateCacheKey(fromProvince, toProvince, provider, expressCode string, weightKg int) string {
	return fmt.Sprintf("%s:%s:%s:%s:%d", fromProvince, toProvince, provider, expressCode, weightKg)
}

// StoreCachedPrice 存储缓存价格，优化并发控制和错误处理
func (r *weightTierCacheRepository) StoreCachedPrice(ctx context.Context, cache *model.WeightTierPriceCache) error {
	// 生成缓存键用于细粒度锁控制
	cacheKey := r.generateCacheKey(cache.FromProvince, cache.ToProvince, cache.Provider, cache.ExpressCode, cache.WeightKg)

	// 使用基于缓存键的细粒度锁，避免全局锁竞争
	keyLock := r.getKeyLock(cacheKey)
	keyLock.Lock()
	defer keyLock.Unlock()

	// 使用重试机制处理并发冲突
	return r.retryWithBackoff(func() error {
		return r.storeCachedPriceWithTransaction(ctx, cache, cacheKey)
	})
}

// storeCachedPriceWithTransaction 在事务中存储缓存价格
func (r *weightTierCacheRepository) storeCachedPriceWithTransaction(ctx context.Context, cache *model.WeightTierPriceCache, cacheKey string) error {
	// 使用较低的隔离级别减少事务冲突
	tx, err := r.db.BeginTx(ctx, &sql.TxOptions{
		Isolation: sql.LevelReadCommitted, // 使用读已提交隔离级别，减少锁竞争
	})
	if err != nil {
		r.logger.Error("开始事务失败", zap.Error(err))
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// 直接使用UPSERT操作，简化逻辑并减少数据库交互
	err = r.upsertCacheRecord(ctx, tx, cache)
	if err != nil {
		return err
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		r.logger.Error("提交事务失败", zap.Error(err))
		return fmt.Errorf("提交事务失败: %w", err)
	}

	// 更新内存缓存
	cache.UpdatedAt = util.NowBeijing()
	cache.IsValid = true
	r.memoryCache.Store(cacheKey, cache)

	r.logger.Info("缓存价格存储成功",
		zap.String("route", fmt.Sprintf("%s->%s", cache.FromProvince, cache.ToProvince)),
		zap.String("provider", cache.Provider),
		zap.String("express_code", cache.ExpressCode),
		zap.String("price", cache.Price.String()),
		zap.Int("weight_kg", cache.WeightKg))

	return nil
}

// upsertCacheRecord 使用UPSERT操作插入或更新缓存记录
func (r *weightTierCacheRepository) upsertCacheRecord(ctx context.Context, tx *sql.Tx, cache *model.WeightTierPriceCache) error {
	// 生成UUID和路由键
	if cache.ID == uuid.Nil {
		cache.ID = r.generateUUID()
	}
	cache.RouteKey = fmt.Sprintf("%s->%s", cache.FromProvince, cache.ToProvince)

	// 从数据库获取快递公司名称
	expressName, err := r.getExpressNameFromDB(ctx, tx, cache.Provider, cache.ExpressCode)
	if err != nil {
		r.logger.Error("获取快递公司名称失败", zap.Error(err),
			zap.String("provider", cache.Provider),
			zap.String("express_code", cache.ExpressCode))
		return fmt.Errorf("获取快递公司名称失败: %w", err)
	}

	// 使用ON CONFLICT DO UPDATE的UPSERT操作
	query := `
		INSERT INTO weight_tier_price_cache
		(id, from_province, to_province, provider, express_code, express_name, weight_kg,
		 price, continued_weight_per_kg, product_code, product_name, channel_id,
		 estimated_days, route_key, source, is_valid,
		 cache_hit_count, validation_count, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		ON CONFLICT (from_province, to_province, provider, express_code, weight_kg)
		DO UPDATE SET
			price = EXCLUDED.price,
			continued_weight_per_kg = EXCLUDED.continued_weight_per_kg,
			product_code = EXCLUDED.product_code,
			product_name = EXCLUDED.product_name,
			channel_id = EXCLUDED.channel_id,
			estimated_days = EXCLUDED.estimated_days,
			route_key = EXCLUDED.route_key,
			source = EXCLUDED.source,
			is_valid = EXCLUDED.is_valid,
			updated_at = CURRENT_TIMESTAMP
	`

	_, err = tx.ExecContext(ctx, query,
		cache.ID, cache.FromProvince, cache.ToProvince, cache.Provider, cache.ExpressCode, expressName,
		cache.WeightKg, cache.Price, cache.ContinuedWeightPerKg, cache.ProductCode, cache.ProductName, cache.ChannelID,
		cache.EstimatedDays, cache.RouteKey, cache.Source, true, 0, 0)

	if err != nil {
		r.logger.Error("UPSERT缓存记录失败", zap.Error(err))
		return fmt.Errorf("UPSERT缓存记录失败: %w", err)
	}

	return nil
}

// 注意：原来的findExistingCache、updateExistingCache和insertNewCacheWithConflictHandling方法
// 已被upsertCacheRecord方法替代，以简化逻辑并减少数据库交互次数

// getExpressNameFromDB 从数据库获取快递公司名称
func (r *weightTierCacheRepository) getExpressNameFromDB(ctx context.Context, tx *sql.Tx, provider, expressCode string) (string, error) {
	// 首先尝试从express_companies表获取
	query := `
		SELECT name
		FROM express_companies
		WHERE code = $1 AND is_active = true
		LIMIT 1
	`

	var expressName string
	err := tx.QueryRowContext(ctx, query, expressCode).Scan(&expressName)
	if err == nil {
		return expressName, nil
	}

	if err != sql.ErrNoRows {
		r.logger.Error("查询express_companies表失败", zap.Error(err))
		return "", fmt.Errorf("查询express_companies表失败: %w", err)
	}

	// 如果express_companies表没有找到，尝试从provider_express_mappings表获取
	query = `
		SELECT express_name
		FROM provider_express_mappings
		WHERE provider = $1 AND express_code = $2 AND is_active = true
		LIMIT 1
	`

	err = tx.QueryRowContext(ctx, query, provider, expressCode).Scan(&expressName)
	if err == nil {
		return expressName, nil
	}

	if err != sql.ErrNoRows {
		r.logger.Error("查询provider_express_mappings表失败", zap.Error(err))
		return "", fmt.Errorf("查询provider_express_mappings表失败: %w", err)
	}

	// 如果都没有找到，记录警告并返回快递代码作为名称
	r.logger.Warn("未找到快递公司名称，使用快递代码",
		zap.String("provider", provider),
		zap.String("express_code", expressCode))

	return expressCode, nil
}

// generateUUID 生成UUID
func (r *weightTierCacheRepository) generateUUID() uuid.UUID {
	return uuid.New()
}

// 统计方法
func (r *weightTierCacheRepository) incrementHitCount() {
	r.stats.mu.Lock()
	r.stats.hitCount++
	r.stats.mu.Unlock()
}

func (r *weightTierCacheRepository) incrementMissCount() {
	r.stats.mu.Lock()
	r.stats.missCount++
	r.stats.mu.Unlock()
}

func (r *weightTierCacheRepository) incrementErrorCount() {
	r.stats.mu.Lock()
	r.stats.errorCount++
	r.stats.mu.Unlock()
}

func (r *weightTierCacheRepository) incrementRetryCount() {
	r.stats.mu.Lock()
	r.stats.retryCount++
	r.stats.mu.Unlock()
}

func (r *weightTierCacheRepository) incrementConflictCount() {
	r.stats.mu.Lock()
	r.stats.conflictCount++
	r.stats.mu.Unlock()
}

// UpdateCachedPrice 更新缓存价格
func (r *weightTierCacheRepository) UpdateCachedPrice(ctx context.Context, cache *model.WeightTierPriceCache) error {
	query := `
		UPDATE weight_tier_price_cache
		SET price = $1, estimated_days = $2, source = $3,
		    is_valid = $4, updated_at = CURRENT_TIMESTAMP
		WHERE from_province = $5 AND to_province = $6 AND provider = $7
		  AND express_code = $8 AND weight_kg = $9
	`

	result, err := r.db.ExecContext(ctx, query,
		cache.Price, cache.EstimatedDays, cache.Source, cache.IsValid,
		cache.FromProvince, cache.ToProvince, cache.Provider, cache.ExpressCode, cache.WeightKg)

	if err != nil {
		r.logger.Error("更新缓存价格失败", zap.Error(err))
		return fmt.Errorf("更新缓存价格失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取影响行数失败", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		r.logger.Warn("没有找到要更新的缓存记录",
			zap.String("route", fmt.Sprintf("%s->%s", cache.FromProvince, cache.ToProvince)),
			zap.String("provider", cache.Provider),
			zap.String("express_code", cache.ExpressCode))
		return fmt.Errorf("没有找到要更新的缓存记录")
	}

	// 更新内存缓存
	cacheKey := r.generateCacheKey(cache.FromProvince, cache.ToProvince, cache.Provider, cache.ExpressCode, cache.WeightKg)
	cache.UpdatedAt = util.NowBeijing()
	r.memoryCache.Store(cacheKey, cache)

	r.logger.Info("缓存价格更新成功",
		zap.String("route", fmt.Sprintf("%s->%s", cache.FromProvince, cache.ToProvince)),
		zap.String("provider", cache.Provider),
		zap.String("express_code", cache.ExpressCode),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}

// InvalidateCache 使缓存失效
func (r *weightTierCacheRepository) InvalidateCache(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weightKg int) error {
	// 1. 从内存缓存中删除
	cacheKey := r.generateCacheKey(fromProvince, toProvince, provider, expressCode, weightKg)
	r.memoryCache.Delete(cacheKey)

	// 2. 更新数据库记录为无效
	query := `
		UPDATE weight_tier_price_cache
		SET is_valid = false, updated_at = CURRENT_TIMESTAMP
		WHERE from_province = $1 AND to_province = $2 AND provider = $3
		  AND express_code = $4 AND weight_kg = $5
	`

	result, err := r.db.ExecContext(ctx, query, fromProvince, toProvince, provider, expressCode, weightKg)
	if err != nil {
		r.logger.Error("使缓存失效失败", zap.Error(err))
		return fmt.Errorf("使缓存失效失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取影响行数失败", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	r.logger.Info("缓存失效成功",
		zap.String("route", fmt.Sprintf("%s->%s", fromProvince, toProvince)),
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int("weight_kg", weightKg),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}

// InvalidateCacheByWeight 按重量批量失效缓存（所有地区）
func (r *weightTierCacheRepository) InvalidateCacheByWeight(ctx context.Context, provider, expressCode string, weightKg int) error {
	// 1. 从内存缓存中删除所有匹配的记录
	r.memoryCache.Range(func(key, value interface{}) bool {
		if keyStr, ok := key.(string); ok {
			// 解析缓存键，检查是否匹配provider、expressCode和weightKg
			if r.isCacheKeyMatch(keyStr, provider, expressCode, weightKg) {
				r.memoryCache.Delete(key)
				r.logger.Debug("从内存缓存中删除",
					zap.String("cache_key", keyStr))
			}
		}
		return true
	})

	// 2. 更新数据库中所有匹配的记录为无效
	query := `
		UPDATE weight_tier_price_cache
		SET is_valid = false, updated_at = CURRENT_TIMESTAMP
		WHERE provider = $1 AND express_code = $2 AND weight_kg = $3
	`

	result, err := r.db.ExecContext(ctx, query, provider, expressCode, weightKg)
	if err != nil {
		r.logger.Error("按重量批量失效缓存失败", zap.Error(err))
		return fmt.Errorf("按重量批量失效缓存失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取影响行数失败", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	r.logger.Info("按重量批量失效缓存成功",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int("weight_kg", weightKg),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}

// isCacheKeyMatch 检查缓存键是否匹配指定的provider、expressCode和weightKg
func (r *weightTierCacheRepository) isCacheKeyMatch(cacheKey, provider, expressCode string, weightKg int) bool {
	// 缓存键格式: "fromProvince:toProvince:provider:expressCode:weightKg"
	// 例如: "湖南省:湖北省:yuntong:STO:1"
	parts := strings.Split(cacheKey, ":")
	if len(parts) != 5 {
		return false
	}

	// 检查provider、expressCode和weightKg是否匹配
	keyProvider := parts[2]
	keyExpressCode := parts[3]
	keyWeightStr := parts[4]

	if keyProvider != provider || keyExpressCode != expressCode {
		return false
	}

	keyWeight, err := strconv.Atoi(keyWeightStr)
	if err != nil {
		return false
	}

	return keyWeight == weightKg
}

// RecordCacheHit 记录缓存命中
func (r *weightTierCacheRepository) RecordCacheHit(ctx context.Context, cacheID string) error {
	query := `
		UPDATE weight_tier_price_cache
		SET cache_hit_count = cache_hit_count + 1,
		    last_hit_time = CURRENT_TIMESTAMP,
		    updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	result, err := r.db.ExecContext(ctx, query, cacheID)
	if err != nil {
		r.logger.Error("记录缓存命中失败", zap.Error(err), zap.String("cache_id", cacheID))
		return fmt.Errorf("记录缓存命中失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取影响行数失败", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected > 0 {
		r.logger.Debug("缓存命中记录成功", zap.String("cache_id", cacheID))
	}

	return nil
}

// RecordCacheValidation 记录缓存验证
func (r *weightTierCacheRepository) RecordCacheValidation(ctx context.Context, cacheID string) error {
	query := `
		UPDATE weight_tier_price_cache
		SET validation_count = validation_count + 1,
		    last_validated_time = CURRENT_TIMESTAMP,
		    updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	result, err := r.db.ExecContext(ctx, query, cacheID)
	if err != nil {
		r.logger.Error("记录缓存验证失败", zap.Error(err), zap.String("cache_id", cacheID))
		return fmt.Errorf("记录缓存验证失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取影响行数失败", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected > 0 {
		r.logger.Debug("缓存验证记录成功", zap.String("cache_id", cacheID))
	}

	return nil
}

// SaveValidationRecord 保存验证记录
func (r *weightTierCacheRepository) SaveValidationRecord(ctx context.Context, validation *model.OrderPriceValidation) error {
	// 生成ID
	if validation.ID == uuid.Nil {
		validation.ID = r.generateUUID()
	}

	query := `
		INSERT INTO order_price_validations
		(id, order_id, provider, express_code, cached_price, realtime_price,
		 price_difference, validation_result, action_taken, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
		ON CONFLICT (order_id, provider)
		DO UPDATE SET
			cached_price = EXCLUDED.cached_price,
			realtime_price = EXCLUDED.realtime_price,
			price_difference = EXCLUDED.price_difference,
			validation_result = EXCLUDED.validation_result,
			action_taken = EXCLUDED.action_taken,
			created_at = CURRENT_TIMESTAMP
	`

	_, err := r.db.ExecContext(ctx, query,
		validation.ID, validation.OrderID, validation.Provider, validation.ExpressCode,
		validation.CachedPrice, validation.RealtimePrice, validation.PriceDifference,
		validation.ValidationResult, validation.ActionTaken)

	if err != nil {
		r.logger.Error("保存价格验证记录失败",
			zap.Error(err),
			zap.String("order_id", validation.OrderID),
			zap.String("validation_result", validation.ValidationResult))
		return fmt.Errorf("保存价格验证记录失败: %w", err)
	}

	r.logger.Info("价格验证记录保存成功",
		zap.String("order_id", validation.OrderID),
		zap.String("validation_result", validation.ValidationResult),
		zap.String("action_taken", validation.ActionTaken))

	return nil
}

// GetValidationHistory 获取验证历史
func (r *weightTierCacheRepository) GetValidationHistory(ctx context.Context, orderID string) ([]*model.OrderPriceValidation, error) {
	query := `
		SELECT id, order_id, provider, express_code, cached_price, realtime_price,
		       price_difference, validation_result, action_taken, created_at
		FROM order_price_validations
		WHERE order_id = $1
		ORDER BY created_at DESC
		LIMIT 50
	`

	rows, err := r.db.QueryContext(ctx, query, orderID)
	if err != nil {
		r.logger.Error("查询验证历史失败", zap.Error(err), zap.String("order_id", orderID))
		return nil, fmt.Errorf("查询验证历史失败: %w", err)
	}
	defer rows.Close()

	var validations []*model.OrderPriceValidation
	for rows.Next() {
		var validation model.OrderPriceValidation
		err := rows.Scan(
			&validation.ID, &validation.OrderID, &validation.Provider, &validation.ExpressCode,
			&validation.CachedPrice, &validation.RealtimePrice, &validation.PriceDifference,
			&validation.ValidationResult, &validation.ActionTaken, &validation.CreatedAt,
		)
		if err != nil {
			r.logger.Error("扫描验证记录失败", zap.Error(err))
			continue
		}
		validations = append(validations, &validation)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历验证记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历验证记录失败: %w", err)
	}

	r.logger.Debug("查询验证历史成功",
		zap.String("order_id", orderID),
		zap.Int("count", len(validations)))

	return validations, nil
}

// GetCacheStatistics 获取缓存统计
func (r *weightTierCacheRepository) GetCacheStatistics(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.WeightCacheStatistics, error) {
	r.logger.Info("开始查询缓存统计",
		zap.Time("start_date", startDate),
		zap.Time("end_date", endDate),
		zap.String("provider", provider))

	var query string
	var args []interface{}

	if provider != "" {
		// 查询特定供应商的缓存统计
		query = `
			SELECT
				date,
				provider,
				total_queries,
				cache_hits,
				cache_misses,
				COALESCE(total_validations, 0) as total_validations,
				COALESCE(exact_matches, 0) as exact_matches,
				validation_failures,
				COALESCE(orders_rejected, 0) as orders_rejected,
				average_response_time_ms
			FROM weight_cache_statistics
			WHERE date >= $1 AND date <= $2 AND provider = $3
			ORDER BY date DESC
			LIMIT 30
		`
		args = []interface{}{startDate, endDate, provider}
	} else {
		// 查询所有供应商的缓存统计
		query = `
			SELECT
				date,
				provider,
				total_queries,
				cache_hits,
				cache_misses,
				COALESCE(total_validations, 0) as total_validations,
				COALESCE(exact_matches, 0) as exact_matches,
				validation_failures,
				COALESCE(orders_rejected, 0) as orders_rejected,
				average_response_time_ms
			FROM weight_cache_statistics
			WHERE date >= $1 AND date <= $2
			ORDER BY date DESC, provider
			LIMIT 100
		`
		args = []interface{}{startDate, endDate}
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		r.logger.Error("查询缓存统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询缓存统计失败: %w", err)
	}
	defer rows.Close()

	var statistics []*model.WeightCacheStatistics
	for rows.Next() {
		var stat model.WeightCacheStatistics

		var totalValidations, exactMatches, ordersRejected int64

		err := rows.Scan(
			&stat.Date,
			&stat.Provider,
			&stat.TotalQueries,
			&stat.CacheHits,
			&stat.CacheMisses,
			&totalValidations,
			&exactMatches,
			&stat.ValidationFailures,
			&ordersRejected,
			&stat.AvgResponseTimeMs,
		)
		if err != nil {
			r.logger.Error("扫描缓存统计记录失败", zap.Error(err))
			continue
		}

		// 设置计算字段
		stat.TotalValidations = totalValidations
		stat.ExactMatches = exactMatches
		stat.OrdersRejected = ordersRejected

		// 计算命中率
		if stat.TotalQueries > 0 {
			stat.HitRate = float64(stat.CacheHits) / float64(stat.TotalQueries) * 100
		}

		statistics = append(statistics, &stat)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历缓存统计记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历缓存统计记录失败: %w", err)
	}

	r.logger.Info("缓存统计查询完成",
		zap.Int("count", len(statistics)))

	return statistics, nil
}

// GetCacheOverview 获取缓存概览
func (r *weightTierCacheRepository) GetCacheOverview(ctx context.Context) ([]*model.WeightCacheOverview, error) {
	r.logger.Info("开始查询缓存概览")

	// 查询所有weight_tier_price_cache表数据，不做任何时间和数量限制
	query := `
		SELECT 
			CONCAT(from_province, '->', to_province) as route,
			provider,
			express_code,
			weight_kg,
			COUNT(*) as cache_count,
			COUNT(CASE WHEN is_valid = true THEN 1 END) as valid_cache_count,
			COUNT(CASE WHEN is_valid = false THEN 1 END) as invalid_cache_count,
			MAX(updated_at) as last_update_time,
			COALESCE(SUM(cache_hit_count), 0) as cache_hit_count,
			COALESCE(SUM(validation_count), 0) as validation_count,
			0 as validation_failure_count,
			COALESCE(AVG(price), 0) as avg_price,
			COALESCE(MIN(price), 0) as min_price,
			COALESCE(MAX(price), 0) as max_price
		FROM weight_tier_price_cache
		GROUP BY from_province, to_province, provider, express_code, weight_kg
		ORDER BY from_province, to_province, provider, express_code, weight_kg
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("查询缓存概览失败", zap.Error(err))
		return nil, fmt.Errorf("查询缓存概览失败: %w", err)
	}
	defer rows.Close()

	var overviews []*model.WeightCacheOverview
	for rows.Next() {
		var overview model.WeightCacheOverview
		var lastUpdateTime sql.NullTime
		var avgPrice, minPrice, maxPrice float64

		err := rows.Scan(
			&overview.Route,
			&overview.Provider,
			&overview.ExpressCode,
			&overview.WeightKg,
			&overview.CacheCount,
			&overview.ValidCacheCount,
			&overview.InvalidCacheCount,
			&lastUpdateTime,
			&overview.CacheHitCount,
			&overview.ValidationCount,
			&overview.ValidationFailureCount,
			&avgPrice,
			&minPrice,
			&maxPrice,
		)
		if err != nil {
			r.logger.Error("扫描缓存概览记录失败", zap.Error(err))
			continue
		}

		// 处理时间字段
		if lastUpdateTime.Valid {
			overview.LastUpdateTime = lastUpdateTime.Time.Format("2006-01-02 15:04:05")
		}

		// 设置价格字段
		overview.AvgPrice = avgPrice
		overview.MinPrice = minPrice
		overview.MaxPrice = maxPrice

		overviews = append(overviews, &overview)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历缓存概览记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历缓存概览记录失败: %w", err)
	}

	r.logger.Info("缓存概览查询完成",
		zap.Int("overview_count", len(overviews)))

	return overviews, nil
}

// GetValidationStats 获取验证统计
func (r *weightTierCacheRepository) GetValidationStats(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.PriceValidationStats, error) {
	r.logger.Info("开始查询验证统计",
		zap.Time("start_date", startDate),
		zap.Time("end_date", endDate),
		zap.String("provider", provider))

	// 使用数据库视图查询验证统计数据
	var query string
	var args []interface{}

	if provider != "" {
		// 查询特定供应商的验证统计
		query = `
			SELECT
				validation_date,
				provider,
				total_validations,
				exact_matches,
				failed_validations,
				avg_price_diff,
				pass_rate
			FROM v_price_validation_stats
			WHERE validation_date >= $1 AND validation_date <= $2 AND provider = $3
			ORDER BY validation_date DESC
			LIMIT 30
		`
		args = []interface{}{startDate, endDate, provider}
	} else {
		// 查询所有供应商的验证统计
		query = `
			SELECT
				validation_date,
				provider,
				total_validations,
				exact_matches,
				failed_validations,
				avg_price_diff,
				pass_rate
			FROM v_price_validation_stats
			WHERE validation_date >= $1 AND validation_date <= $2
			ORDER BY validation_date DESC, pass_rate ASC
			LIMIT 50
		`
		args = []interface{}{startDate, endDate}
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		r.logger.Error("查询验证统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询验证统计失败: %w", err)
	}
	defer rows.Close()

	var stats []*model.PriceValidationStats
	for rows.Next() {
		var stat model.PriceValidationStats

		err := rows.Scan(
			&stat.ValidationDate,
			&stat.Provider,
			&stat.TotalValidations,
			&stat.PassedValidations,
			&stat.FailedValidations,
			&stat.AvgPriceDiff,
			&stat.PassRate,
		)
		if err != nil {
			r.logger.Error("扫描验证统计记录失败", zap.Error(err))
			continue
		}

		stats = append(stats, &stat)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历验证统计记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历验证统计记录失败: %w", err)
	}

	r.logger.Info("验证统计查询完成",
		zap.Int("stats_count", len(stats)))

	return stats, nil
}

// GetActiveRoutes 获取活跃路线
func (r *weightTierCacheRepository) GetActiveRoutes(ctx context.Context) ([]*model.RouteDefinition, error) {
	// 简化实现：返回一些基本路线
	routes := []*model.RouteDefinition{
		{RouteKey: "北京市->上海市"},
		{RouteKey: "上海市->广东省"},
		{RouteKey: "广东省->北京市"},
	}
	return routes, nil
}

// GetRouteByProvinces 根据省份获取路线
func (r *weightTierCacheRepository) GetRouteByProvinces(ctx context.Context, fromProvince, toProvince string) (*model.RouteDefinition, error) {
	// 简化实现：返回基本路线
	return &model.RouteDefinition{
		RouteKey: fmt.Sprintf("%s->%s", fromProvince, toProvince),
	}, nil
}

// CleanupInvalidCache 清理无效缓存
func (r *weightTierCacheRepository) CleanupInvalidCache(ctx context.Context) error {
	// 简化实现：记录日志但不实际清理
	r.logger.Info("清理无效缓存（简化版）")
	return nil
}

// GetCacheSize 获取缓存大小
func (r *weightTierCacheRepository) GetCacheSize(ctx context.Context) (int64, error) {
	// 简化实现：返回0
	return 0, nil
}

// SavePriceQueryLog 保存价格查询日志
func (r *weightTierCacheRepository) SavePriceQueryLog(ctx context.Context, logReq *model.WeightTierQueryLogRequest) error {
	query := `
		INSERT INTO weight_tier_query_logs (
			from_province, to_province, provider, express_code, weight_kg,
			price, source, response_time_ms, success, error_message, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
	`

	_, err := r.db.ExecContext(ctx, query,
		logReq.FromProvince,
		logReq.ToProvince,
		logReq.Provider,
		logReq.ExpressCode,
		logReq.WeightKg,
		logReq.Price,
		logReq.Source,
		logReq.ResponseTimeMs,
		logReq.Success,
		logReq.ErrorMessage,
	)

	if err != nil {
		r.logger.Error("保存价格查询日志失败",
			zap.Error(err),
			zap.String("provider", logReq.Provider),
			zap.String("source", logReq.Source),
			zap.Bool("success", logReq.Success))
		return fmt.Errorf("保存价格查询日志失败: %w", err)
	}

	r.logger.Debug("价格查询日志保存成功",
		zap.String("provider", logReq.Provider),
		zap.String("source", logReq.Source),
		zap.Bool("success", logReq.Success),
		zap.String("error_message", logReq.ErrorMessage))

	return nil
}

// GetDailyCacheStats 获取每日缓存统计
func (r *weightTierCacheRepository) GetDailyCacheStats(ctx context.Context, startDate, endDate time.Time, provider string) ([]*model.DailyCacheStats, error) {
	// 简化实现：返回空列表
	return []*model.DailyCacheStats{}, nil
}

// GetRuntimeStatistics 获取运行时缓存统计信息
func (r *weightTierCacheRepository) GetRuntimeStatistics() map[string]interface{} {
	r.stats.mu.RLock()
	defer r.stats.mu.RUnlock()

	return map[string]interface{}{
		"hit_count":      r.stats.hitCount,
		"miss_count":     r.stats.missCount,
		"error_count":    r.stats.errorCount,
		"retry_count":    r.stats.retryCount,
		"conflict_count": r.stats.conflictCount,
		"last_reset":     r.stats.lastResetTime,
		"hit_rate":       r.calculateHitRate(),
	}
}

// calculateHitRate 计算缓存命中率
func (r *weightTierCacheRepository) calculateHitRate() float64 {
	total := r.stats.hitCount + r.stats.missCount
	if total == 0 {
		return 0.0
	}
	return float64(r.stats.hitCount) / float64(total) * 100.0
}

// getKeyLock 获取基于缓存键的锁
func (r *weightTierCacheRepository) getKeyLock(cacheKey string) *sync.Mutex {
	lock, _ := r.keyLocks.LoadOrStore(cacheKey, &sync.Mutex{})
	return lock.(*sync.Mutex)
}

// retryWithBackoff 带指数退避的重试机制
func (r *weightTierCacheRepository) retryWithBackoff(operation func() error) error {
	var lastErr error

	for attempt := 0; attempt < maxRetryAttempts; attempt++ {
		if err := operation(); err != nil {
			lastErr = err

			// 检查是否是可重试的错误
			if !r.isRetryableError(err) {
				return err
			}

			// 统计并发冲突
			r.incrementConflictCount()

			if attempt < maxRetryAttempts-1 {
				// 统计重试次数
				r.incrementRetryCount()

				// 计算退避延迟：基础延迟 * 2^attempt + 随机抖动
				delay := baseRetryDelay * (1 << attempt)
				if delay > maxRetryDelay {
					delay = maxRetryDelay
				}

				// 添加随机抖动 (±25%)
				jitter := delay / 4
				if jitter > 0 {
					delay += (rand.Intn(2*jitter) - jitter)
				}

				r.logger.Warn("缓存操作失败，准备重试",
					zap.Error(err),
					zap.Int("attempt", attempt+1),
					zap.Int("max_attempts", maxRetryAttempts),
					zap.Int("delay_ms", delay))

				time.Sleep(time.Duration(delay) * time.Millisecond)
			}
		} else {
			// 操作成功
			if attempt > 0 {
				r.logger.Info("缓存操作重试成功",
					zap.Int("successful_attempt", attempt+1))
			}
			return nil
		}
	}

	return fmt.Errorf("缓存操作在%d次重试后仍然失败: %w", maxRetryAttempts, lastErr)
}

// isRetryableError 判断错误是否可重试
func (r *weightTierCacheRepository) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// PostgreSQL序列化访问错误是可重试的
	return strings.Contains(errStr, "could not serialize access") ||
		strings.Contains(errStr, "concurrent update") ||
		strings.Contains(errStr, "deadlock detected")
}

// GetExpressCodes 获取快递代码列表
func (r *weightTierCacheRepository) GetExpressCodes(ctx context.Context, provider string) ([]string, error) {
	// 简化实现：返回基本快递代码
	codes := []string{"ZTO", "YTO", "STO", "YD", "JT", "JD", "DBL"}
	return codes, nil
}

// GetProviders 获取供应商列表
func (r *weightTierCacheRepository) GetProviders(ctx context.Context) ([]string, error) {
	// 简化实现：返回基本供应商
	providers := []string{"kuaidi100", "yida", "yuntong"}
	return providers, nil
}

// GetWeightTiers 获取重量档位列表
func (r *weightTierCacheRepository) GetWeightTiers(ctx context.Context) ([]int, error) {
	// 简化实现：返回1-20的重量档位
	weights := make([]int, 20)
	for i := 0; i < 20; i++ {
		weights[i] = i + 1
	}
	return weights, nil
}

// GetProviderGroupedOverview 获取按供应商分组的缓存概览
func (r *weightTierCacheRepository) GetProviderGroupedOverview(ctx context.Context) ([]*model.ProviderGroup, error) {
	r.logger.Info("开始查询按供应商分组的缓存概览")

	// 查询供应商级别的统计数据
	query := `
		SELECT
			provider,
			COUNT(*) as total_cache_count,
			COUNT(CASE WHEN is_valid = true THEN 1 END) as valid_cache_count,
			COUNT(CASE WHEN is_valid = false THEN 1 END) as invalid_cache_count,
			COALESCE(SUM(cache_hit_count), 0) as total_hit_count,
			COALESCE(SUM(validation_count), 0) as total_validation_count,
			COALESCE(SUM(validation_count - CASE WHEN validation_count > 0 THEN 1 ELSE 0 END), 0) as total_validation_failure_count,
			COALESCE(AVG(price::numeric), 0) as avg_price,
			COALESCE(MIN(price::numeric), 0) as min_price,
			COALESCE(MAX(price::numeric), 0) as max_price,
			MAX(updated_at) as last_update_time
		FROM weight_tier_price_cache
		GROUP BY provider
		ORDER BY provider
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("查询供应商分组概览失败", zap.Error(err))
		return nil, fmt.Errorf("查询供应商分组概览失败: %w", err)
	}
	defer rows.Close()

	var providerGroups []*model.ProviderGroup
	for rows.Next() {
		var group model.ProviderGroup
		var lastUpdateTime sql.NullTime
		var avgPrice, minPrice, maxPrice float64

		err := rows.Scan(
			&group.Provider,
			&group.TotalCacheCount,
			&group.ValidCacheCount,
			&group.InvalidCacheCount,
			&group.TotalHitCount,
			&group.TotalValidationCount,
			&group.TotalValidationFailureCount,
			&avgPrice,
			&minPrice,
			&maxPrice,
			&lastUpdateTime,
		)
		if err != nil {
			r.logger.Error("扫描供应商分组记录失败", zap.Error(err))
			continue
		}

		// 设置供应商名称
		group.ProviderName = r.getProviderDisplayName(group.Provider)
		group.AvgPrice = avgPrice
		group.MinPrice = minPrice
		group.MaxPrice = maxPrice

		// 处理时间字段
		if lastUpdateTime.Valid {
			group.LastUpdateTime = lastUpdateTime.Time.Format("2006-01-02 15:04:05")
		}

		// 获取该供应商下的快递公司信息
		expressCompanies, err := r.getExpressCompaniesByProvider(ctx, group.Provider)
		if err != nil {
			r.logger.Error("获取供应商快递公司失败",
				zap.String("provider", group.Provider),
				zap.Error(err))
			continue
		}
		group.ExpressCompanies = expressCompanies

		providerGroups = append(providerGroups, &group)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历供应商分组记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历供应商分组记录失败: %w", err)
	}

	r.logger.Info("供应商分组概览查询完成",
		zap.Int("provider_count", len(providerGroups)))

	return providerGroups, nil
}

// getProviderDisplayName 获取供应商显示名称
func (r *weightTierCacheRepository) getProviderDisplayName(provider string) string {
	displayNames := map[string]string{
		"kuaidi100": "快递100",
		"yida":      "易达",
		"yuntong":   "云通",
		"cainiao":   "菜鸟",
	}
	if name, exists := displayNames[provider]; exists {
		return name
	}
	return provider
}

// getExpressCompaniesByProvider 获取供应商下的快递公司信息
func (r *weightTierCacheRepository) getExpressCompaniesByProvider(ctx context.Context, provider string) ([]*model.ExpressCompanyCache, error) {
	// 直接查询原始表，简化维护
	query := `
		SELECT
			express_code,
			-- 从快递公司表获取快递公司名称
			COALESCE(ec.name, express_code) as express_name,
			COUNT(*) as cache_count,
			COUNT(CASE WHEN is_valid = true THEN 1 END) as valid_cache_count,
			COUNT(CASE WHEN is_valid = false THEN 1 END) as invalid_cache_count,
			COALESCE(SUM(cache_hit_count), 0) as hit_count,
			COALESCE(SUM(validation_count), 0) as validation_count,
			COALESCE(SUM(validation_count - CASE WHEN validation_count > 0 THEN 1 ELSE 0 END), 0) as validation_failure_count,
			COALESCE(AVG(price::numeric), 0) as avg_price,
			COALESCE(MIN(price::numeric), 0) as min_price,
			COALESCE(MAX(price::numeric), 0) as max_price,
			MAX(wtpc.updated_at) as last_update_time
		FROM weight_tier_price_cache wtpc
		LEFT JOIN express_companies ec ON wtpc.express_code = ec.code
		WHERE wtpc.provider = $1
		GROUP BY express_code, ec.name
		ORDER BY express_code
	`

	rows, err := r.db.QueryContext(ctx, query, provider)
	if err != nil {
		return nil, fmt.Errorf("查询快递公司信息失败: %w", err)
	}
	defer rows.Close()

	var companies []*model.ExpressCompanyCache
	for rows.Next() {
		var company model.ExpressCompanyCache
		var lastUpdateTime sql.NullTime
		var avgPrice, minPrice, maxPrice float64

		err := rows.Scan(
			&company.ExpressCode,
			&company.ExpressName,
			&company.CacheCount,
			&company.ValidCacheCount,
			&company.InvalidCacheCount,
			&company.HitCount,
			&company.ValidationCount,
			&company.ValidationFailureCount,
			&avgPrice,
			&minPrice,
			&maxPrice,
			&lastUpdateTime,
		)
		if err != nil {
			r.logger.Error("扫描快递公司记录失败", zap.Error(err))
			continue
		}

		company.AvgPrice = avgPrice
		company.MinPrice = minPrice
		company.MaxPrice = maxPrice

		// 处理时间字段
		if lastUpdateTime.Valid {
			company.LastUpdateTime = lastUpdateTime.Time.Format("2006-01-02 15:04:05")
		}

		// 🚀 获取路线信息（使用物化视图，限制数量以避免数据过多）
		routes, err := r.getRoutesByProviderAndExpress(ctx, provider, company.ExpressCode, 10)
		if err != nil {
			r.logger.Error("获取路线信息失败",
				zap.String("provider", provider),
				zap.String("express_code", company.ExpressCode),
				zap.Error(err))
		} else {
			company.Routes = routes
		}

		companies = append(companies, &company)
	}

	return companies, nil
}

// getRoutesByProviderAndExpress 获取指定供应商和快递公司的路线信息
func (r *weightTierCacheRepository) getRoutesByProviderAndExpress(ctx context.Context, provider, expressCode string, limit int) ([]*model.RouteCacheInfo, error) {
	// 直接查询原始表
	query := `
		SELECT
			from_province || '->' || to_province as route,
			from_province,
			to_province,
			weight_kg,
			price,
			continued_weight_per_kg,
			COALESCE(product_code, '') as product_code,
			COALESCE(product_name, '') as product_name,
			COALESCE(channel_id, '') as channel_id,
			estimated_days,
			cache_hit_count,
			validation_count,
			CASE WHEN validation_count > 0 THEN validation_count - 1 ELSE 0 END as validation_failure_count,
			is_valid,
			CASE WHEN last_hit_time IS NOT NULL THEN last_hit_time::text ELSE '' END as last_hit_time,
			CASE WHEN last_validated_time IS NOT NULL THEN last_validated_time::text ELSE '' END as last_validated_time,
			created_at::text,
			updated_at::text
		FROM weight_tier_price_cache
		WHERE provider = $1 AND express_code = $2
		ORDER BY updated_at DESC
		LIMIT $3
	`

	rows, err := r.db.QueryContext(ctx, query, provider, expressCode, limit)
	if err != nil {
		return nil, fmt.Errorf("查询路线信息失败: %w", err)
	}
	defer rows.Close()

	var routes []*model.RouteCacheInfo
	for rows.Next() {
		var route model.RouteCacheInfo
		var lastHitTime, lastValidatedTime sql.NullString

		err := rows.Scan(
			&route.Route,
			&route.FromProvince,
			&route.ToProvince,
			&route.WeightKg,
			&route.Price,
			&route.ContinuedWeightPerKg,
			&route.ProductCode,
			&route.ProductName,
			&route.ChannelID,
			&route.EstimatedDays,
			&route.CacheHitCount,
			&route.ValidationCount,
			&route.ValidationFailureCount,
			&route.IsValid,
			&lastHitTime,
			&lastValidatedTime,
			&route.CreatedAt,
			&route.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("扫描路线记录失败", zap.Error(err))
			continue
		}

		// 处理可空时间字段
		if lastHitTime.Valid {
			route.LastHitTime = &lastHitTime.String
		}
		if lastValidatedTime.Valid {
			route.LastValidatedTime = &lastValidatedTime.String
		}

		routes = append(routes, &route)
	}

	return routes, nil
}

// GetCacheDetails 获取缓存详情（包含失败记录统计）
func (r *weightTierCacheRepository) GetCacheDetails(ctx context.Context, req *model.CacheDetailRequest) ([]*model.RouteCacheInfo, int64, error) {
	r.logger.Info("开始查询缓存详情",
		zap.String("provider", req.Provider),
		zap.String("express_code", req.ExpressCode),
		zap.String("route", req.Route))

	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("c.provider = $%d", argIndex))
		args = append(args, req.Provider)
		argIndex++
	}

	if req.ExpressCode != "" {
		conditions = append(conditions, fmt.Sprintf("c.express_code = $%d", argIndex))
		args = append(args, req.ExpressCode)
		argIndex++
	}

	if req.Route != "" {
		conditions = append(conditions, fmt.Sprintf("(c.from_province || '->' || c.to_province) = $%d", argIndex))
		args = append(args, req.Route)
		argIndex++
	}

	if req.FromProvince != "" {
		conditions = append(conditions, fmt.Sprintf("c.from_province = $%d", argIndex))
		args = append(args, req.FromProvince)
		argIndex++
	}

	if req.ToProvince != "" {
		conditions = append(conditions, fmt.Sprintf("c.to_province = $%d", argIndex))
		args = append(args, req.ToProvince)
		argIndex++
	}

	if req.WeightKg != nil {
		conditions = append(conditions, fmt.Sprintf("c.weight_kg = $%d", argIndex))
		args = append(args, *req.WeightKg)
		argIndex++
	}

	if req.IsValid != nil {
		conditions = append(conditions, fmt.Sprintf("c.is_valid = $%d", argIndex))
		args = append(args, *req.IsValid)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 构建排序条件
	orderBy := "updated_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]string{
			"price":            "price",
			"updated_at":       "updated_at",
			"hit_count":        "cache_hit_count",
			"validation_count": "validation_count",
		}
		if field, exists := validSortFields[req.SortBy]; exists {
			order := "DESC"
			if req.SortOrder == "asc" {
				order = "ASC"
			}
			orderBy = fmt.Sprintf("%s %s", field, order)
		}
	}

	// 设置分页参数
	page := req.Page
	if page < 1 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	offset := (page - 1) * pageSize

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM weight_tier_price_cache c
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("查询缓存详情总数失败", zap.Error(err))
		return nil, 0, fmt.Errorf("查询缓存详情总数失败: %w", err)
	}

	// 查询详情数据（包含失败记录统计）
	dataQuery := fmt.Sprintf(`
		SELECT
			c.from_province || '->' || c.to_province as route,
			c.from_province,
			c.to_province,
			c.weight_kg,
			c.price,
			c.continued_weight_per_kg,
			COALESCE(c.product_code, '') as product_code,
			COALESCE(c.product_name, '') as product_name,
			COALESCE(c.channel_id, '') as channel_id,
			c.estimated_days,
			c.cache_hit_count,
			c.validation_count,
			CASE WHEN c.validation_count > 0 THEN c.validation_count - 1 ELSE 0 END as validation_failure_count,
			c.is_valid,
			CASE WHEN c.last_hit_time IS NOT NULL THEN c.last_hit_time::text ELSE NULL END as last_hit_time,
			CASE WHEN c.last_validated_time IS NOT NULL THEN c.last_validated_time::text ELSE NULL END as last_validated_time,
			c.created_at::text,
			c.updated_at::text,
			COALESCE(l.failed_queries, 0) as failed_queries,
			COALESCE(l.total_queries, 0) as total_queries
		FROM weight_tier_price_cache c
		LEFT JOIN (
			SELECT
				from_province, to_province, provider, express_code, weight_kg,
				COUNT(*) as total_queries,
				COUNT(CASE WHEN success = false THEN 1 END) as failed_queries
			FROM weight_tier_query_logs
			WHERE created_at >= NOW() - INTERVAL '30 days'
			GROUP BY from_province, to_province, provider, express_code, weight_kg
		) l ON c.from_province = l.from_province
			AND c.to_province = l.to_province
			AND c.provider = l.provider
			AND c.express_code = l.express_code
			AND c.weight_kg = l.weight_kg
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, pageSize, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		r.logger.Error("查询缓存详情数据失败", zap.Error(err))
		return nil, 0, fmt.Errorf("查询缓存详情数据失败: %w", err)
	}
	defer rows.Close()

	var routes []*model.RouteCacheInfo
	for rows.Next() {
		var route model.RouteCacheInfo
		var lastHitTime, lastValidatedTime sql.NullString

		err := rows.Scan(
			&route.Route,
			&route.FromProvince,
			&route.ToProvince,
			&route.WeightKg,
			&route.Price,
			&route.ContinuedWeightPerKg,
			&route.ProductCode,
			&route.ProductName,
			&route.ChannelID,
			&route.EstimatedDays,
			&route.CacheHitCount,
			&route.ValidationCount,
			&route.ValidationFailureCount,
			&route.IsValid,
			&lastHitTime,
			&lastValidatedTime,
			&route.CreatedAt,
			&route.UpdatedAt,
			&route.FailedQueries,
			&route.TotalQueries,
		)
		if err != nil {
			r.logger.Error("扫描缓存详情记录失败", zap.Error(err))
			continue
		}

		// 处理可空时间字段
		if lastHitTime.Valid {
			route.LastHitTime = &lastHitTime.String
		}
		if lastValidatedTime.Valid {
			route.LastValidatedTime = &lastValidatedTime.String
		}

		// 🚀 获取失败原因（仅当有失败记录时）
		if route.FailedQueries > 0 {
			failureReasons, err := r.getFailureReasons(ctx, route.FromProvince, route.ToProvince, req.Provider, req.ExpressCode, route.WeightKg)
			if err != nil {
				r.logger.Error("获取失败原因失败",
					zap.String("route", route.Route),
					zap.Int("weight_kg", route.WeightKg),
					zap.Error(err))
			} else {
				route.FailureReasons = failureReasons
			}
		}

		routes = append(routes, &route)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历缓存详情记录失败", zap.Error(err))
		return nil, 0, fmt.Errorf("遍历缓存详情记录失败: %w", err)
	}

	r.logger.Info("缓存详情查询完成",
		zap.Int64("total", total),
		zap.Int("records", len(routes)),
		zap.Int("page", page),
		zap.Int("page_size", pageSize))

	return routes, total, nil
}

// getFailureReasons 获取指定路线的失败原因
func (r *weightTierCacheRepository) getFailureReasons(ctx context.Context, fromProvince, toProvince, provider, expressCode string, weightKg int) ([]*model.FailureReasonInfo, error) {
	query := `
		SELECT
			error_message,
			COUNT(*) as count,
			MAX(created_at)::text as last_occurred,
			source
		FROM weight_tier_query_logs
		WHERE from_province = $1
			AND to_province = $2
			AND provider = $3
			AND express_code = $4
			AND weight_kg = $5
			AND success = false
			AND error_message IS NOT NULL
			AND error_message != ''
			AND created_at >= NOW() - INTERVAL '30 days'
		GROUP BY error_message, source
		ORDER BY count DESC, last_occurred DESC
		LIMIT 10
	`

	rows, err := r.db.QueryContext(ctx, query, fromProvince, toProvince, provider, expressCode, weightKg)
	if err != nil {
		r.logger.Error("查询失败原因失败", zap.Error(err))
		return nil, fmt.Errorf("查询失败原因失败: %w", err)
	}
	defer rows.Close()

	var reasons []*model.FailureReasonInfo
	for rows.Next() {
		var reason model.FailureReasonInfo
		err := rows.Scan(
			&reason.ErrorMessage,
			&reason.Count,
			&reason.LastOccurred,
			&reason.Source,
		)
		if err != nil {
			r.logger.Error("扫描失败原因记录失败", zap.Error(err))
			continue
		}
		reasons = append(reasons, &reason)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历失败原因记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历失败原因记录失败: %w", err)
	}

	return reasons, nil
}

// BatchInvalidateCache 批量清理缓存
func (r *weightTierCacheRepository) BatchInvalidateCache(ctx context.Context, provider, expressCode, route string) error {
	r.logger.Info("开始批量清理缓存",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.String("route", route),
		zap.Bool("is_provider_only", provider != "" && expressCode == "" && route == ""),
		zap.Bool("is_company_specific", provider != "" && expressCode != "" && route == ""))

	// 构建删除条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, provider)
		argIndex++
	}

	if expressCode != "" {
		conditions = append(conditions, fmt.Sprintf("express_code = $%d", argIndex))
		args = append(args, expressCode)
		argIndex++
	}

	if route != "" {
		conditions = append(conditions, fmt.Sprintf("(from_province || '->' || to_province) = $%d", argIndex))
		args = append(args, route)
		argIndex++
	}

	if len(conditions) == 0 {
		return fmt.Errorf("批量清理缓存需要至少一个筛选条件")
	}

	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	// 先查询要删除的记录数量
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM weight_tier_price_cache
		%s
	`, whereClause)

	var count int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&count)
	if err != nil {
		r.logger.Error("查询待删除缓存数量失败", zap.Error(err))
		return fmt.Errorf("查询待删除缓存数量失败: %w", err)
	}

	if count == 0 {
		r.logger.Info("没有找到匹配的缓存记录")
		return nil
	}

	// 执行删除操作
	deleteQuery := fmt.Sprintf(`
		DELETE FROM weight_tier_price_cache
		%s
	`, whereClause)

	result, err := r.db.ExecContext(ctx, deleteQuery, args...)
	if err != nil {
		r.logger.Error("批量删除缓存失败", zap.Error(err))
		return fmt.Errorf("批量删除缓存失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("获取删除行数失败", zap.Error(err))
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	// 清理内存缓存
	r.clearMemoryCache()

	r.logger.Info("批量清理缓存完成",
		zap.Int64("expected_count", count),
		zap.Int64("rows_affected", rowsAffected),
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.String("route", route))

	return nil
}

// InvalidateCacheByPattern 按模式失效缓存
func (r *weightTierCacheRepository) InvalidateCacheByPattern(ctx context.Context, provider, expressCode string) error {
	r.logger.Info("开始按模式失效缓存",
		zap.String("provider", provider),
		zap.String("express_code", expressCode))

	// 1. 从内存缓存中移除匹配的条目
	deletedCount := 0
	r.memoryCache.Range(func(key, value interface{}) bool {
		keyStr := key.(string)
		// 修复：使用正确的匹配模式，缓存键格式为 fromProvince:toProvince:provider:expressCode:weight
		// 我们需要匹配 ":provider:expressCode:" 这个模式，这样可以精确匹配而不会误删
		pattern := fmt.Sprintf(":%s:%s:", provider, expressCode)
		if strings.Contains(keyStr, pattern) {
			r.memoryCache.Delete(key)
			deletedCount++
			r.logger.Debug("从内存缓存删除条目",
				zap.String("cache_key", keyStr),
				zap.String("provider", provider),
				zap.String("express_code", expressCode))
		}
		return true
	})

	r.logger.Info("内存缓存清理完成",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int("deleted_count", deletedCount))

	// 2. 从数据库中标记失效
	query := `
		UPDATE weight_tier_price_cache
		SET is_valid = false, updated_at = NOW()
		WHERE provider = $1 AND express_code = $2 AND is_valid = true
	`

	result, err := r.db.ExecContext(ctx, query, provider, expressCode)
	if err != nil {
		r.logger.Error("数据库批量失效缓存失败",
			zap.Error(err),
			zap.String("provider", provider),
			zap.String("express_code", expressCode))
		return fmt.Errorf("数据库批量失效缓存失败: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	r.logger.Info("按模式失效缓存完成",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Int64("db_rows_affected", rowsAffected),
		zap.Int("memory_deleted_count", deletedCount))

	return nil
}

// ClearAllCache 清空所有缓存
func (r *weightTierCacheRepository) ClearAllCache(ctx context.Context) error {
	r.logger.Info("开始清空所有缓存")

	// 1. 清空内存缓存
	r.memoryCache.Range(func(key, value interface{}) bool {
		r.memoryCache.Delete(key)
		return true
	})

	// 2. 标记数据库中所有缓存为失效
	query := `UPDATE weight_tier_price_cache SET is_valid = false, updated_at = NOW() WHERE is_valid = true`

	result, err := r.db.ExecContext(ctx, query)
	if err != nil {
		r.logger.Error("清空数据库缓存失败", zap.Error(err))
		return fmt.Errorf("清空数据库缓存失败: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	r.logger.Info("清空所有缓存完成", zap.Int64("rows_affected", rowsAffected))

	return nil
}

// GetCacheStats 获取缓存统计信息
func (r *weightTierCacheRepository) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取内存缓存统计
	memoryCount := 0
	r.memoryCache.Range(func(key, value interface{}) bool {
		memoryCount++
		return true
	})

	// 获取数据库缓存统计
	var dbValidCount, dbInvalidCount int64
	query := `
		SELECT
			COUNT(CASE WHEN is_valid = true THEN 1 END) as valid_count,
			COUNT(CASE WHEN is_valid = false THEN 1 END) as invalid_count
		FROM weight_tier_price_cache
	`

	err := r.db.QueryRowContext(ctx, query).Scan(&dbValidCount, &dbInvalidCount)
	if err != nil {
		r.logger.Error("获取数据库缓存统计失败", zap.Error(err))
		return nil, fmt.Errorf("获取数据库缓存统计失败: %w", err)
	}

	// 获取缓存命中统计
	r.stats.mu.RLock()
	hitCount := r.stats.hitCount
	missCount := r.stats.missCount
	errorCount := r.stats.errorCount
	r.stats.mu.RUnlock()

	stats["memory_cache_count"] = memoryCount
	stats["db_valid_count"] = dbValidCount
	stats["db_invalid_count"] = dbInvalidCount
	stats["hit_count"] = hitCount
	stats["miss_count"] = missCount
	stats["error_count"] = errorCount

	if hitCount+missCount > 0 {
		stats["hit_rate"] = float64(hitCount) / float64(hitCount+missCount) * 100
	} else {
		stats["hit_rate"] = 0.0
	}

	return stats, nil
}

// clearMemoryCache 清理内存缓存
func (r *weightTierCacheRepository) clearMemoryCache() {
	// 清理内存缓存
	r.memoryCache.Range(func(key, value interface{}) bool {
		r.memoryCache.Delete(key)
		return true
	})
	r.logger.Info("内存缓存已清理")
}

// 🚀 GetCacheOverviewOptimized 获取缓存概览（优化版本）
func (r *weightTierCacheRepository) GetCacheOverviewOptimized(ctx context.Context, req *model.CacheOverviewRequest) (*model.CacheOverviewResponse, error) {
	r.logger.Info("开始查询缓存概览（优化版本）",
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize),
		zap.String("provider", req.Provider))

	// 直接查询原始表，简化维护
	query := `
		SELECT
			CONCAT(from_province, '->', to_province) as route,
			provider,
			express_code,
			weight_kg,
			COUNT(*) as cache_count,
			COUNT(CASE WHEN is_valid = true THEN 1 END) as valid_cache_count,
			COUNT(CASE WHEN is_valid = false THEN 1 END) as invalid_cache_count,
			MAX(updated_at) as last_update_time,
			COALESCE(SUM(cache_hit_count), 0) as cache_hit_count,
			COALESCE(SUM(validation_count), 0) as validation_count,
			COALESCE(SUM(validation_count - CASE WHEN validation_count > 0 THEN 1 ELSE 0 END), 0) as validation_failure_count,
			COALESCE(AVG(price::numeric), 0) as avg_price,
			COALESCE(MIN(price::numeric), 0) as min_price,
			COALESCE(MAX(price::numeric), 0) as max_price,
			NOW() as refreshed_at
		FROM weight_tier_price_cache
		WHERE 1=1
	`

	var args []interface{}
	argCount := 0

	// 供应商筛选
	if req.Provider != "" {
		argCount++
		query += fmt.Sprintf(" AND provider = $%d", argCount)
		args = append(args, req.Provider)
	}

	// 快递公司筛选
	if req.ExpressCode != "" {
		argCount++
		query += fmt.Sprintf(" AND express_code = $%d", argCount)
		args = append(args, req.ExpressCode)
	}

	// 路线筛选
	if req.Route != "" {
		argCount++
		query += fmt.Sprintf(" AND (from_province || '->' || to_province) ILIKE $%d", argCount)
		args = append(args, "%"+req.Route+"%")
	}

	// GROUP BY 子句
	query += " GROUP BY from_province, to_province, provider, express_code, weight_kg"

	// 排序
	query += " ORDER BY provider, express_code, from_province, to_province, weight_kg"

	// 分页
	if req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		argCount++
		query += fmt.Sprintf(" LIMIT $%d", argCount)
		args = append(args, req.PageSize)

		argCount++
		query += fmt.Sprintf(" OFFSET $%d", argCount)
		args = append(args, offset)
	}

	start := time.Now()
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		r.logger.Error("查询优化缓存概览失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return nil, fmt.Errorf("查询缓存概览失败: %w", err)
	}
	defer rows.Close()

	var overviews []*model.WeightCacheOverview
	for rows.Next() {
		var overview model.WeightCacheOverview
		var lastUpdateTime sql.NullTime
		var refreshedAt sql.NullTime

		err := rows.Scan(
			&overview.Route,
			&overview.Provider,
			&overview.ExpressCode,
			&overview.WeightKg,
			&overview.CacheCount,
			&overview.ValidCacheCount,
			&overview.InvalidCacheCount,
			&lastUpdateTime,
			&overview.CacheHitCount,
			&overview.ValidationCount,
			&overview.ValidationFailureCount,
			&overview.AvgPrice,
			&overview.MinPrice,
			&overview.MaxPrice,
			&refreshedAt,
		)
		if err != nil {
			r.logger.Error("扫描缓存概览记录失败", zap.Error(err))
			continue
		}

		// 处理时间字段
		if lastUpdateTime.Valid {
			overview.LastUpdateTime = lastUpdateTime.Time.Format("2006-01-02 15:04:05")
		}
		if refreshedAt.Valid {
			overview.RefreshedAt = refreshedAt.Time.Format("2006-01-02 15:04:05")
		}

		overviews = append(overviews, &overview)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历缓存概览记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历缓存概览记录失败: %w", err)
	}

	// 获取总数
	totalCount, err := r.getCacheOverviewCount(ctx, req)
	if err != nil {
		r.logger.Warn("获取总数失败，使用已查询数量", zap.Error(err))
		totalCount = int64(len(overviews))
	}

	duration := time.Since(start)
	r.logger.Info("优化缓存概览查询完成",
		zap.Int("overview_count", len(overviews)),
		zap.Int64("total_count", totalCount),
		zap.Duration("duration", duration))

	response := &model.CacheOverviewResponse{
		Success: true,
		Code:    200,
		Message: "获取缓存概览成功",
	}
	response.Data.Data = overviews
	response.Data.TotalCount = totalCount
	response.Data.Page = req.Page
	response.Data.PageSize = req.PageSize
	response.Data.TotalPages = int((totalCount + int64(req.PageSize) - 1) / int64(req.PageSize))

	return response, nil
}

// getCacheOverviewCount 获取缓存概览总数
func (r *weightTierCacheRepository) getCacheOverviewCount(ctx context.Context, req *model.CacheOverviewRequest) (int64, error) {
	// 使用子查询统计分组后的记录数
	query := `
		SELECT COUNT(*) FROM (
			SELECT 1
			FROM weight_tier_price_cache
			WHERE 1=1
	`
	var args []interface{}
	argCount := 0

	if req.Provider != "" {
		argCount++
		query += fmt.Sprintf(" AND provider = $%d", argCount)
		args = append(args, req.Provider)
	}

	if req.ExpressCode != "" {
		argCount++
		query += fmt.Sprintf(" AND express_code = $%d", argCount)
		args = append(args, req.ExpressCode)
	}

	if req.Route != "" {
		argCount++
		query += fmt.Sprintf(" AND (from_province || '->' || to_province) ILIKE $%d", argCount)
		args = append(args, "%"+req.Route+"%")
	}

	// 完成子查询
	query += " GROUP BY from_province, to_province, provider, express_code, weight_kg) as grouped_data"

	var count int64
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询总数失败: %w", err)
	}

	return count, nil
}

// 🚀 GetProviderGroupedOverviewOptimized 获取供应商分组概览（优化版本）
func (r *weightTierCacheRepository) GetProviderGroupedOverviewOptimized(ctx context.Context) ([]*model.ProviderGroup, error) {
	r.logger.Info("开始查询优化供应商分组概览")

	// 直接查询原始表，按供应商分组统计
	query := `
		SELECT
			provider,
			COUNT(*) as total_cache_count,
			COUNT(CASE WHEN is_valid = true THEN 1 END) as valid_cache_count,
			COUNT(CASE WHEN is_valid = false THEN 1 END) as invalid_cache_count,
			COALESCE(SUM(cache_hit_count), 0) as total_hit_count,
			COALESCE(SUM(validation_count), 0) as total_validation_count,
			COALESCE(AVG(price::numeric), 0) as avg_price,
			COALESCE(MIN(price::numeric), 0) as min_price,
			COALESCE(MAX(price::numeric), 0) as max_price,
			MAX(updated_at) as last_update_time,
			NOW() as refreshed_at
		FROM weight_tier_price_cache
		GROUP BY provider
		ORDER BY provider
	`

	start := time.Now()
	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("查询优化供应商分组概览失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return nil, fmt.Errorf("查询供应商分组概览失败: %w", err)
	}
	defer rows.Close()

	var providerGroups []*model.ProviderGroup
	for rows.Next() {
		var group model.ProviderGroup
		var lastUpdateTime sql.NullTime
		var refreshedAt sql.NullTime

		err := rows.Scan(
			&group.Provider,
			&group.TotalCacheCount,
			&group.ValidCacheCount,
			&group.InvalidCacheCount,
			&group.TotalHitCount,
			&group.TotalValidationCount,
			&group.AvgPrice,
			&group.MinPrice,
			&group.MaxPrice,
			&lastUpdateTime,
			&refreshedAt,
		)
		if err != nil {
			r.logger.Error("扫描供应商分组记录失败", zap.Error(err))
			continue
		}

		// 处理时间字段
		if lastUpdateTime.Valid {
			group.LastUpdateTime = lastUpdateTime.Time.Format("2006-01-02 15:04:05")
		}
		if refreshedAt.Valid {
			group.RefreshedAt = refreshedAt.Time.Format("2006-01-02 15:04:05")
		}

		// 设置供应商显示名称
		group.ProviderName = r.getProviderDisplayName(group.Provider)

		// 🚀 获取该供应商下的快递公司信息
		expressCompanies, err := r.getExpressCompaniesByProvider(ctx, group.Provider)
		if err != nil {
			r.logger.Error("获取供应商快递公司失败",
				zap.String("provider", group.Provider),
				zap.Error(err))
			// 不跳过，设置为空数组继续处理
			group.ExpressCompanies = []*model.ExpressCompanyCache{}
		} else {
			group.ExpressCompanies = expressCompanies
		}

		providerGroups = append(providerGroups, &group)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("遍历供应商分组记录失败", zap.Error(err))
		return nil, fmt.Errorf("遍历供应商分组记录失败: %w", err)
	}

	duration := time.Since(start)
	r.logger.Info("优化供应商分组概览查询完成",
		zap.Int("provider_count", len(providerGroups)),
		zap.Int("total_companies", func() int {
			total := 0
			for _, group := range providerGroups {
				total += len(group.ExpressCompanies)
			}
			return total
		}()),
		zap.Duration("duration", duration))

	return providerGroups, nil
}

// 🚀 RefreshCacheViews 刷新缓存视图
func (r *weightTierCacheRepository) RefreshCacheViews(ctx context.Context) error {
	r.logger.Info("开始刷新缓存物化视图")

	start := time.Now()
	_, err := r.db.ExecContext(ctx, "SELECT refresh_weight_cache_views()")
	if err != nil {
		r.logger.Error("刷新缓存视图失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(start)))
		return fmt.Errorf("刷新缓存视图失败: %w", err)
	}

	duration := time.Since(start)
	r.logger.Info("缓存视图刷新完成", zap.Duration("duration", duration))
	return nil
}

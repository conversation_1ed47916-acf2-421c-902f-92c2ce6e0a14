package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
)

// WeightTierCacheHandler 重量档位缓存处理器
type WeightTierCacheHandler struct {
	cacheService service.WeightTierCacheService
	logger       *zap.Logger
}

// NewWeightTierCacheHandler 创建重量档位缓存处理器
func NewWeightTierCacheHandler(cacheService service.WeightTierCacheService, logger *zap.Logger) *WeightTierCacheHandler {
	return &WeightTierCacheHandler{
		cacheService: cacheService,
		logger:       logger,
	}
}

// RegisterRoutes 注册路由
func (h *WeightTierCacheHandler) RegisterRoutes(r *gin.RouterGroup) {
	cache := r.Group("/weight-cache")
	{
		// 核心功能
		cache.POST("/query", h.QueryPriceWithCache)   // 带缓存的价格查询
		cache.POST("/validate", h.ValidateOrderPrice) // 订单价格验证

		// 缓存管理
		cache.DELETE("/invalidate", h.InvalidateCache)                       // 使缓存失效
		cache.POST("/warmup", h.WarmupCache)                                 // 预热缓存
		cache.POST("/warmup/progress", h.GetWarmupProgress)                  // 获取预热进度
		cache.DELETE("/cleanup", h.CleanupInvalidCache)                      // 清理无效缓存
		cache.DELETE("/clear-provider-company", h.ClearProviderCompanyCache) // 🚀 清除供应商+快递公司缓存

		// 统计查询
		cache.GET("/statistics", h.GetCacheStatistics)               // 获取缓存统计
		cache.GET("/overview", h.GetCacheOverview)                   // 获取缓存概览
		cache.GET("/overview/grouped", h.GetProviderGroupedOverview) // 获取按供应商分组的缓存概览
		cache.GET("/details", h.GetCacheDetails)                     // 获取缓存详情
		cache.GET("/validation-stats", h.GetValidationStats)         // 获取验证统计

		// 🚀 优化后的接口
		cache.GET("/overview-optimized", h.GetCacheOverviewOptimized)                   // 获取缓存概览（优化版本）
		cache.GET("/overview/grouped-optimized", h.GetProviderGroupedOverviewOptimized) // 获取供应商分组概览（优化版本）
		cache.GET("/quick-stats", h.GetQuickStats)                                      // 获取快速统计
		cache.POST("/refresh-views", h.RefreshCacheViews)                               // 刷新缓存视图

		// 批量管理
		cache.DELETE("/batch-invalidate", h.BatchInvalidateCache) // 批量清理缓存
	}
}

// QueryPriceWithCache 带缓存的价格查询
// @Summary 重量档位缓存价格查询
// @Description 查询价格，优先从缓存获取，缓存未命中时调用实时API并异步缓存结果
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param request body model.WeightTierCacheRequest true "查询请求"
// @Success 200 {object} model.WeightTierCacheResponse "查询成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/query [post]
func (h *WeightTierCacheHandler) QueryPriceWithCache(c *gin.Context) {
	var req model.WeightTierCacheRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.WeightTierCacheResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	h.logger.Info("接收到缓存价格查询请求",
		zap.String("route", req.FromProvince+"->"+req.ToProvince),
		zap.String("provider", req.Provider),
		zap.String("express_code", req.ExpressCode),
		zap.Float64("weight", req.Weight))

	resp, err := h.cacheService.QueryPriceWithCache(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("缓存价格查询失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.WeightTierCacheResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询失败: " + err.Error(),
		})
		return
	}

	// 根据响应成功状态确定HTTP状态码
	statusCode := http.StatusOK
	if !resp.Success {
		switch resp.Code {
		case model.StatusNotFound:
			statusCode = http.StatusNotFound
		case model.StatusInternalServerError:
			statusCode = http.StatusInternalServerError
		default:
			statusCode = http.StatusBadRequest
		}
	}

	c.JSON(statusCode, resp)
}

// ValidateOrderPrice 订单价格验证
// @Summary 订单价格验证
// @Description 验证订单价格与实时价格是否一致，不一致时使缓存失效并要求重新查价
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param request body model.OrderValidationRequest true "验证请求"
// @Success 200 {object} model.OrderValidationResponse "验证成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 409 {object} model.OrderValidationResponse "价格不一致"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/validate [post]
func (h *WeightTierCacheHandler) ValidateOrderPrice(c *gin.Context) {
	var req model.OrderValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	h.logger.Info("接收到订单价格验证请求",
		zap.String("order_id", req.OrderID),
		zap.String("price_source", req.PriceSource))

	resp, err := h.cacheService.ValidateOrderPrice(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("订单价格验证失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.OrderValidationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "验证失败: " + err.Error(),
		})
		return
	}

	// 根据验证结果确定HTTP状态码
	statusCode := http.StatusOK
	if !resp.Success {
		switch resp.Code {
		case model.StatusNotFound:
			statusCode = http.StatusNotFound
		case model.StatusConflict:
			statusCode = http.StatusConflict // 价格不一致
		case model.StatusInternalServerError:
			statusCode = http.StatusInternalServerError
		default:
			statusCode = http.StatusBadRequest
		}
	}

	c.JSON(statusCode, resp)
}

// InvalidateCache 使缓存失效
// @Summary 使缓存失效
// @Description 使指定路线和重量的缓存条目失效
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param from_province query string true "起始省份"
// @Param to_province query string true "目标省份"
// @Param provider query string true "供应商"
// @Param express_code query string true "快递代码"
// @Param weight query number true "重量"
// @Success 200 {object} model.ApiResponse "操作成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/invalidate [delete]
func (h *WeightTierCacheHandler) InvalidateCache(c *gin.Context) {
	fromProvince := c.Query("from_province")
	toProvince := c.Query("to_province")
	provider := c.Query("provider")
	expressCode := c.Query("express_code")
	weightStr := c.Query("weight")

	if fromProvince == "" || toProvince == "" || provider == "" || expressCode == "" || weightStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "缺少必要参数",
		})
		return
	}

	weight, err := strconv.ParseFloat(weightStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "重量参数格式错误",
		})
		return
	}

	h.logger.Info("接收到缓存失效请求",
		zap.String("route", fromProvince+"->"+toProvince),
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Float64("weight", weight))

	err = h.cacheService.InvalidateCacheEntry(c.Request.Context(), fromProvince, toProvince, provider, expressCode, weight)
	if err != nil {
		h.logger.Error("缓存失效操作失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "操作失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "缓存失效成功",
	})
}

// WarmupCache 预热缓存
// @Summary 预热缓存
// @Description 批量预热指定路线和重量的缓存数据
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param request body object true "预热请求"
// @Success 200 {object} model.ApiResponse "操作成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/warmup [post]
func (h *WeightTierCacheHandler) WarmupCache(c *gin.Context) {
	var req struct {
		Routes       []string `json:"routes"`        // 路线列表，格式: ["北京->上海", "上海->广东"]
		Providers    []string `json:"providers"`     // 供应商列表
		Weights      []int    `json:"weights"`       // 重量列表
		ExpressCodes []string `json:"express_codes"` // 快递代码列表
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	h.logger.Info("接收到缓存预热请求",
		zap.Int("routes_count", len(req.Routes)),
		zap.Int("providers_count", len(req.Providers)),
		zap.Int("weights_count", len(req.Weights)),
		zap.Int("express_codes_count", len(req.ExpressCodes)))

	// 构造路线定义（简化版，实际应该从数据库获取）
	var routes []*model.RouteDefinition
	for _, routeStr := range req.Routes {
		// 这里需要解析路线字符串，简化处理
		routes = append(routes, &model.RouteDefinition{
			RouteKey: routeStr,
		})
	}

	err := h.cacheService.WarmupCache(c.Request.Context(), routes, req.Providers, req.Weights, req.ExpressCodes)
	if err != nil {
		h.logger.Error("缓存预热失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "预热失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "缓存预热已启动",
		"data": gin.H{
			"total_tasks":   len(routes) * len(req.Providers) * len(req.Weights) * len(req.ExpressCodes),
			"routes":        len(routes),
			"providers":     len(req.Providers),
			"weights":       len(req.Weights),
			"express_codes": len(req.ExpressCodes),
		},
	})
}

// GetWarmupProgress 获取预热进度
// @Summary 获取预热进度
// @Description 获取当前缓存预热的进度信息
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} model.ApiResponse "操作成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/warmup/progress [post]
func (h *WeightTierCacheHandler) GetWarmupProgress(c *gin.Context) {
	// 这里可以实现预热进度跟踪
	// 目前返回基本信息
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"data": gin.H{
			"status":      "running",
			"progress":    0,
			"total_tasks": 0,
			"completed":   0,
			"failed":      0,
			"message":     "预热进度跟踪功能开发中",
		},
	})
}

// CleanupInvalidCache 清理无效缓存
// @Summary 清理无效缓存
// @Description 清理标记为无效的缓存数据
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} model.ApiResponse "操作成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/cleanup [delete]
func (h *WeightTierCacheHandler) CleanupInvalidCache(c *gin.Context) {
	h.logger.Info("接收到清理无效缓存请求")

	err := h.cacheService.CleanupInvalidCache(c.Request.Context())
	if err != nil {
		h.logger.Error("清理无效缓存失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "清理失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "清理无效缓存成功",
	})
}

// GetCacheStatistics 获取缓存统计
// @Summary 获取缓存统计
// @Description 获取指定时间范围内的缓存命中率和验证统计
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期" format(date)
// @Param end_date query string true "结束日期" format(date)
// @Param provider query string false "供应商过滤"
// @Success 200 {object} model.CacheStatisticsResponse "查询成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/statistics [get]
func (h *WeightTierCacheHandler) GetCacheStatistics(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	provider := c.Query("provider")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "缺少日期参数",
		})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "开始日期格式错误",
		})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "结束日期格式错误",
		})
		return
	}

	req := &model.CacheStatisticsRequest{
		StartDate: startDate,
		EndDate:   endDate,
		Provider:  provider,
	}

	h.logger.Info("接收到缓存统计查询请求",
		zap.String("start_date", startDateStr),
		zap.String("end_date", endDateStr),
		zap.String("provider", provider))

	resp, err := h.cacheService.GetCacheStatistics(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("获取缓存统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetCacheOverview 获取缓存概览
// @Summary 获取缓存概览
// @Description 获取所有路线的缓存覆盖情况和统计信息
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} object "查询成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/overview [get]
func (h *WeightTierCacheHandler) GetCacheOverview(c *gin.Context) {
	h.logger.Info("接收到缓存概览查询请求")

	overview, err := h.cacheService.GetCacheOverview(c.Request.Context())
	if err != nil {
		h.logger.Error("获取缓存概览失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "查询成功",
		"data":    overview,
	})
}

// GetValidationStats 获取验证统计
// @Summary 获取价格验证统计
// @Description 获取指定时间范围内的价格验证成功率统计
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期" format(date)
// @Param end_date query string true "结束日期" format(date)
// @Param provider query string false "供应商过滤"
// @Success 200 {object} object "查询成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/validation-stats [get]
func (h *WeightTierCacheHandler) GetValidationStats(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	provider := c.Query("provider")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "缺少日期参数",
		})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "开始日期格式错误",
		})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "结束日期格式错误",
		})
		return
	}

	h.logger.Info("接收到验证统计查询请求",
		zap.String("start_date", startDateStr),
		zap.String("end_date", endDateStr),
		zap.String("provider", provider))

	stats, err := h.cacheService.GetValidationStats(c.Request.Context(), startDate, endDate, provider)
	if err != nil {
		h.logger.Error("获取验证统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "查询成功",
		"data":    stats,
	})
}

// GetProviderGroupedOverview 获取按供应商分组的缓存概览
// @Summary 获取按供应商分组的缓存概览
// @Description 获取按供应商分组的缓存概览，包含每个供应商下的快递公司信息
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} object "查询成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/overview/grouped [get]
func (h *WeightTierCacheHandler) GetProviderGroupedOverview(c *gin.Context) {
	h.logger.Info("接收到按供应商分组的缓存概览查询请求")

	groups, err := h.cacheService.GetProviderGroupedOverview(c.Request.Context())
	if err != nil {
		h.logger.Error("获取供应商分组概览失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "查询成功",
		"data":    groups,
	})
}

// GetCacheDetails 获取缓存详情
// @Summary 获取缓存详情
// @Description 获取缓存详情，支持按供应商、快递公司、地区等维度筛选和排序
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param provider query string false "供应商"
// @Param express_code query string false "快递代码"
// @Param route query string false "路线"
// @Param from_province query string false "发货省份"
// @Param to_province query string false "收货省份"
// @Param weight_kg query int false "重量档位"
// @Param is_valid query bool false "是否有效"
// @Param sort_by query string false "排序字段" Enums(price, updated_at, hit_count, validation_count)
// @Param sort_order query string false "排序方向" Enums(asc, desc)
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} model.CacheDetailResponse "查询成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/details [get]
func (h *WeightTierCacheHandler) GetCacheDetails(c *gin.Context) {
	var req model.CacheDetailRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	h.logger.Info("接收到缓存详情查询请求",
		zap.String("provider", req.Provider),
		zap.String("express_code", req.ExpressCode),
		zap.String("route", req.Route),
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize))

	resp, err := h.cacheService.GetCacheDetails(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("获取缓存详情失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// BatchInvalidateCache 批量清理缓存
// @Summary 批量清理缓存
// @Description 批量清理缓存，支持按供应商、快递公司、路线等维度批量清理
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param provider query string false "供应商"
// @Param express_code query string false "快递代码"
// @Param route query string false "路线"
// @Success 200 {object} model.ApiResponse "操作成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/batch-invalidate [delete]
func (h *WeightTierCacheHandler) BatchInvalidateCache(c *gin.Context) {
	provider := c.Query("provider")
	expressCode := c.Query("express_code")
	route := c.Query("route")

	// 至少需要一个筛选条件
	if provider == "" && expressCode == "" && route == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "至少需要提供一个筛选条件（供应商、快递代码或路线）",
		})
		return
	}

	h.logger.Info("接收到批量清理缓存请求",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.String("route", route))

	err := h.cacheService.BatchInvalidateCache(c.Request.Context(), provider, expressCode, route)
	if err != nil {
		h.logger.Error("批量清理缓存失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "操作失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "批量清理缓存成功",
	})
}

// 🚀 GetCacheOverviewOptimized 获取缓存概览（优化版本）
// @Summary 获取缓存概览（优化版本）
// @Description 获取缓存概览，支持分页和筛选，使用物化视图优化性能
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(50)
// @Param provider query string false "供应商筛选"
// @Param route query string false "路线筛选"
// @Success 200 {object} object "查询成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/overview-optimized [get]
func (h *WeightTierCacheHandler) GetCacheOverviewOptimized(c *gin.Context) {
	h.logger.Info("接收到优化缓存概览查询请求")

	// 解析分页参数
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 50
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	provider := c.Query("provider")
	expressCode := c.Query("express_code")
	route := c.Query("route")

	req := &model.CacheOverviewRequest{
		Page:        page,
		PageSize:    pageSize,
		Provider:    provider,
		ExpressCode: expressCode,
		Route:       route,
	}

	response, err := h.cacheService.GetCacheOverviewOptimized(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("获取优化缓存概览失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "查询成功",
		"data":    response.Data, // 🔧 修复：返回response.Data而不是整个response对象
	})
}

// 🚀 GetProviderGroupedOverviewOptimized 获取供应商分组概览（优化版本）
// @Summary 获取供应商分组概览（优化版本）
// @Description 获取按供应商分组的缓存概览，使用物化视图优化性能
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} object "查询成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/overview/grouped-optimized [get]
func (h *WeightTierCacheHandler) GetProviderGroupedOverviewOptimized(c *gin.Context) {
	h.logger.Info("接收到优化供应商分组概览查询请求")

	groups, err := h.cacheService.GetProviderGroupedOverviewOptimized(c.Request.Context())
	if err != nil {
		h.logger.Error("获取优化供应商分组概览失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "查询成功",
		"data":    groups,
	})
}

// 🚀 GetQuickStats 获取快速统计
// @Summary 获取快速统计
// @Description 获取重量缓存的快速统计信息，响应时间 < 100ms
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} model.QuickStatsResponse "查询成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/quick-stats [get]
func (h *WeightTierCacheHandler) GetQuickStats(c *gin.Context) {
	h.logger.Info("接收到快速统计查询请求")

	response, err := h.cacheService.GetQuickStats(c.Request.Context())
	if err != nil {
		h.logger.Error("获取快速统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// 🚀 RefreshCacheViews 刷新缓存视图
// @Summary 刷新缓存视图
// @Description 手动刷新物化视图，更新缓存统计数据
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Success 200 {object} object "刷新成功"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/refresh-views [post]
func (h *WeightTierCacheHandler) RefreshCacheViews(c *gin.Context) {
	h.logger.Info("接收到刷新缓存视图请求")

	start := time.Now()
	err := h.cacheService.RefreshCacheViews(c.Request.Context())
	duration := time.Since(start)

	if err != nil {
		h.logger.Error("刷新缓存视图失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "刷新失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": "缓存视图刷新成功",
		"data": gin.H{
			"duration": duration.Milliseconds(),
			"message":  fmt.Sprintf("刷新完成，耗时 %dms", duration.Milliseconds()),
		},
	})
}

// 🚀 ClearProviderCompanyCache 清除供应商+快递公司的所有缓存
// @Summary 清除供应商+快递公司的所有缓存
// @Description 清除指定供应商和快递公司的所有缓存数据
// @Tags 重量档位缓存
// @Accept json
// @Produce json
// @Param provider query string true "供应商"
// @Param express_code query string true "快递代码"
// @Success 200 {object} model.ApiResponse "操作成功"
// @Failure 400 {object} model.ApiResponse "请求参数错误"
// @Failure 500 {object} model.ApiResponse "服务器内部错误"
// @Router /api/v1/weight-cache/clear-provider-company [delete]
func (h *WeightTierCacheHandler) ClearProviderCompanyCache(c *gin.Context) {
	provider := c.Query("provider")
	expressCode := c.Query("express_code")

	// 验证必需参数
	if provider == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "供应商参数不能为空",
		})
		return
	}

	if expressCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    model.StatusBadRequest,
			"message": "快递代码参数不能为空",
		})
		return
	}

	h.logger.Info("接收到清除供应商+快递公司缓存请求",
		zap.String("provider", provider),
		zap.String("express_code", expressCode))

	// 使用批量清理方法，传入供应商和快递公司参数
	err := h.cacheService.BatchInvalidateCache(c.Request.Context(), provider, expressCode, "")
	if err != nil {
		h.logger.Error("清除供应商+快递公司缓存失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    model.StatusInternalServerError,
			"message": "操作失败: " + err.Error(),
		})
		return
	}

	// 🚀 清除缓存后立即刷新物化视图，确保前端数据一致性
	h.logger.Info("开始刷新物化视图以更新统计数据")
	refreshErr := h.cacheService.RefreshCacheViews(c.Request.Context())
	if refreshErr != nil {
		h.logger.Warn("刷新物化视图失败，但缓存清除成功", zap.Error(refreshErr))
		// 不影响主要操作的成功，只记录警告
	} else {
		h.logger.Info("物化视图刷新成功")
	}

	h.logger.Info("清除供应商+快递公司缓存成功",
		zap.String("provider", provider),
		zap.String("express_code", expressCode))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    model.StatusSuccess,
		"message": fmt.Sprintf("已成功清除 %s - %s 的所有缓存", provider, expressCode),
	})
}

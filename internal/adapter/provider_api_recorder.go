package adapter

import (
	"context"
	"github.com/your-org/go-kuaidi/internal/model"
)

// orderAttemptRecorder 是适配器层的可选回调，用于记录供应商API请求快照
var orderAttemptRecorder func(ctx context.Context, attemptReq *model.OrderAttemptRequest) error

// SetOrderAttemptRecorder 设置记录器，由上层服务（OrderService）在初始化时注入
func SetOrderAttemptRecorder(recorder func(ctx context.Context, attemptReq *model.OrderAttemptRequest) error) {
	orderAttemptRecorder = recorder
}

// RecordOrderAttempt 供适配器在关键点调用，持久化下单尝试（不会影响主流程）
func RecordOrderAttempt(ctx context.Context, attemptReq *model.OrderAttemptRequest) {
	if orderAttemptRecorder != nil && attemptReq != nil {
		_ = orderAttemptRecorder(ctx, attemptReq)
	}
}


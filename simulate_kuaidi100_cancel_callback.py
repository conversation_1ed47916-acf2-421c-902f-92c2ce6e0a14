#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.parse
import json
import requests
import hashlib
import time
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:8081"
CALLBACK_ENDPOINT = "/api/v1/callbacks/kuaidi100"

# 运单信息（从数据库查询得到）
ORDER_INFO = {
    "tracking_no": "JT0018430273902",
    "platform_order_no": "GK20250825000000147", 
    "customer_order_no": "9618700_157",
    "order_no": "303321823",
    "express_type": "JT",
    "task_id": "17F97342B0BD76AEBE2FA1B39AA13F22",
    "poll_token": "mzyqO2SW/GrcCdVG03INecrxiUuM2voB2ejsATJ3RkQ="
}

def generate_kuaidi100_cancel_callback():
    """生成快递100已取消回调数据"""
    
    # 基于真实格式构建取消回调数据
    callback_data = {
        "data": {
            "orderId": ORDER_INFO["order_no"],
            "courierName": "系统取消",
            "courierMobile": "",
            "defPrice": None,
            "mktId": None,
            "price": None,
            "freight": None,
            "weight": None,
            "pollToken": ORDER_INFO["poll_token"],
            "thirdOrderId": ORDER_INFO["platform_order_no"],
            "status": 99,  # 99 = 已取消
            "updateTime": int(time.time() * 1000),  # 当前时间戳（毫秒）
            "sentStatus": 99,  # 发送状态：已取消
            "userCancel": True,  # 用户主动取消
            "payStatus": 0,  # 支付状态
            "trace": "订单已取消"
        },
        "kuaidicom": ORDER_INFO["express_type"].lower(),
        "kuaidinum": ORDER_INFO["tracking_no"],
        "message": "成功",
        "status": "200"
    }
    
    return callback_data

def generate_callback_signature(param_json, task_id):
    """生成快递100回调签名（模拟）"""
    # 这里使用简单的MD5签名，实际可能需要根据快递100的签名算法调整
    sign_string = f"{param_json}{task_id}"
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()

def send_cancel_callback():
    """发送取消回调"""
    print("🔄 模拟快递100已取消回调")
    print("=" * 60)
    
    # 1. 生成回调数据
    callback_data = generate_kuaidi100_cancel_callback()
    param_json = json.dumps(callback_data, separators=(',', ':'), ensure_ascii=False)
    
    print(f"📦 订单信息:")
    print(f"   运单号: {ORDER_INFO['tracking_no']}")
    print(f"   平台订单号: {ORDER_INFO['platform_order_no']}")
    print(f"   客户订单号: {ORDER_INFO['customer_order_no']}")
    print(f"   快递100订单号: {ORDER_INFO['order_no']}")
    
    print(f"\n📋 回调数据:")
    print(json.dumps(callback_data, indent=2, ensure_ascii=False))
    
    # 2. 生成签名
    signature = generate_callback_signature(param_json, ORDER_INFO["task_id"])
    
    # 3. 构建POST数据（模拟快递100回调格式）
    post_data = {
        "param": param_json,
        "sign": signature,
        "taskId": ORDER_INFO["task_id"]
    }
    
    # URL编码
    encoded_data = urllib.parse.urlencode(post_data, quote_via=urllib.parse.quote)
    
    print(f"\n📤 发送回调请求:")
    print(f"   URL: {API_BASE_URL}{CALLBACK_ENDPOINT}")
    print(f"   签名: {signature}")
    print(f"   任务ID: {ORDER_INFO['task_id']}")
    
    # 4. 发送请求
    try:
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Kuaidi100-Cancel-Simulation/1.0',
            'X-Simulate-Callback': 'true',
            'X-Cancel-Order': ORDER_INFO['tracking_no'],
            'X-Simulate-Timestamp': str(int(time.time()))
        }
        
        response = requests.post(
            f"{API_BASE_URL}{CALLBACK_ENDPOINT}",
            data=encoded_data,
            headers=headers,
            timeout=30
        )
        
        print(f"\n📊 响应结果:")
        print(f"   HTTP状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 取消回调发送成功")
            return True
        else:
            print("❌ 取消回调发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求发送失败: {str(e)}")
        return False

def verify_order_status():
    """验证订单状态是否已更新"""
    print(f"\n🔍 验证订单状态更新...")
    
    # 这里可以添加查询数据库验证订单状态的逻辑
    # 暂时只打印提示信息
    print(f"   请手动检查运单号 {ORDER_INFO['tracking_no']} 的状态是否已更新为'已取消'")

if __name__ == "__main__":
    print("🚀 快递100取消回调模拟工具")
    print("=" * 60)
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 发送取消回调
    success = send_cancel_callback()
    
    if success:
        # 验证状态更新
        verify_order_status()
        
        print(f"\n✅ 模拟取消回调完成")
        print(f"📝 建议后续操作:")
        print(f"   1. 检查数据库中订单状态是否已更新")
        print(f"   2. 检查回调转发记录是否正常")
        print(f"   3. 检查用户余额是否已退款")
    else:
        print(f"\n❌ 模拟取消回调失败")
        print(f"📝 请检查:")
        print(f"   1. API服务是否正常运行")
        print(f"   2. 回调端点是否正确")
        print(f"   3. 网络连接是否正常")

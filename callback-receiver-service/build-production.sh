#!/bin/bash

# =====================================================================
# 回调接收服务生产环境部署脚本
# 功能：
#   1. 构建两个二进制文件：callback-receiver、callback-consumer
#   2. 生成生产环境配置文件
#   3. 创建启动脚本和服务管理脚本
#   4. 打包完整的部署包
# 使用：
#   ./build-production.sh [版本号]
#   示例：./build-production.sh v1.2.3
# =====================================================================

set -euo pipefail

# 配置
SERVICE_DIR="$(cd "$(dirname "$0")" && pwd)"
DIST_DIR="$(cd "$(dirname "$0")" && pwd)/dist"
VERSION="${1:-$(date +%Y%m%d_%H%M%S)}"
PACKAGE_NAME="callback-receiver-production-$VERSION"

# 生产环境配置
MAIN_SYSTEM_URL="https://mywl.py258.com"
HEALTH_CHECK_URL="https://mywl.py258.com/health"

# 颜色输出
G='\033[0;32m'; R='\033[0;31m'; Y='\033[1;33m'; B='\033[0;34m'; N='\033[0m'
log() { echo -e "${G}[INFO]${N} $*"; }
warn() { echo -e "${Y}[WARN]${N} $*"; }
err() { echo -e "${R}[ERROR]${N} $*" >&2; }
info() { echo -e "${B}[BUILD]${N} $*"; }

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

OS_TYPE=$(detect_os)

info "构建生产环境部署包:"
info "  版本: $VERSION"
info "  操作系统: $OS_TYPE"
info "  主系统URL: $MAIN_SYSTEM_URL"
info "  包名: $PACKAGE_NAME"

# 环境检查
check_environment() {
    log "检查构建环境..."

    # 检查必要工具
    command -v go >/dev/null 2>&1 || { err "未安装 Go"; exit 1; }

    # 检查Go版本
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log "Go版本: $GO_VERSION"

    # 检查目录结构
    [ -d "$SERVICE_DIR" ] || { err "服务目录不存在: $SERVICE_DIR"; exit 1; }
    [ -f "$SERVICE_DIR/go.mod" ] || { err "缺少 go.mod"; exit 1; }
    [ -f "$SERVICE_DIR/config.json" ] || { err "缺少 config.json"; exit 1; }
    [ -d "$SERVICE_DIR/cmd" ] || { err "缺少 cmd 目录"; exit 1; }
    [ -f "$SERVICE_DIR/cmd/main.go" ] || { err "缺少 cmd/main.go"; exit 1; }
    [ -d "$SERVICE_DIR/cmd/consumer" ] || { err "缺少 cmd/consumer 目录"; exit 1; }
    [ -f "$SERVICE_DIR/cmd/consumer/main.go" ] || { err "缺少 cmd/consumer/main.go"; exit 1; }

    # 获取Git信息
    if command -v git >/dev/null 2>&1 && git rev-parse --git-dir > /dev/null 2>&1; then
        GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        log "Git提交: $GIT_COMMIT"
        log "Git分支: $GIT_BRANCH"
    else
        warn "不在Git仓库中，跳过版本信息"
        GIT_COMMIT="unknown"
        GIT_BRANCH="unknown"
    fi
}

check_environment

# 构建二进制文件
build_binaries() {
    log "准备构建环境..."

    # 清理并创建构建目录
    rm -rf "$DIST_DIR" && mkdir -p "$DIST_DIR/$PACKAGE_NAME"
    cd "$SERVICE_DIR"

    # 构建信息
    BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
    LDFLAGS="-s -w -X 'main.Version=$VERSION' -X 'main.BuildTime=$BUILD_TIME' -X 'main.GitCommit=$GIT_COMMIT' -X 'main.GitBranch=$GIT_BRANCH'"

    log "构建 callback-receiver..."
    info "  目标平台: linux/amd64"
    info "  版本信息: $VERSION"
    info "  构建时间: $BUILD_TIME"

    if ! CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
        -trimpath \
        -ldflags="$LDFLAGS" \
        -o "$DIST_DIR/$PACKAGE_NAME/callback-receiver" \
        ./cmd; then
        err "构建 callback-receiver 失败"
        exit 1
    fi

    log "构建 callback-consumer..."
    if ! CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
        -trimpath \
        -ldflags="$LDFLAGS" \
        -o "$DIST_DIR/$PACKAGE_NAME/callback-consumer" \
        ./cmd/consumer/main.go; then
        err "构建 callback-consumer 失败"
        exit 1
    fi

    # 验证二进制文件
    log "验证二进制文件..."
    for binary in callback-receiver callback-consumer; do
        if ! file "$DIST_DIR/$PACKAGE_NAME/$binary" | grep -q "ELF.*executable"; then
            err "$binary 不是有效的 Linux 可执行文件"
            exit 1
        fi

        # 显示文件大小
        SIZE=$(du -sh "$DIST_DIR/$PACKAGE_NAME/$binary" | cut -f1)
        log "  $binary: $SIZE"
    done
}

build_binaries

# 生成生产环境配置文件
generate_config() {
    log "生成生产环境配置文件..."

    # 复制原始配置文件
    if ! cp "$SERVICE_DIR/config.json" "$DIST_DIR/$PACKAGE_NAME/config.json"; then
        err "复制配置文件失败"
        exit 1
    fi

    # 根据操作系统使用不同的sed语法
    if [[ "$OS_TYPE" == "macos" ]]; then
        # macOS 语法
        sed -i '' "s|\"url\": \"http://localhost:8081\"|\"url\": \"$MAIN_SYSTEM_URL\"|g" "$DIST_DIR/$PACKAGE_NAME/config.json"
        sed -i '' "s|\"url\": \"http://localhost:8081/health\"|\"url\": \"$HEALTH_CHECK_URL\"|g" "$DIST_DIR/$PACKAGE_NAME/config.json"
    else
        # Linux 语法
        sed -i "s|\"url\": \"http://localhost:8081\"|\"url\": \"$MAIN_SYSTEM_URL\"|g" "$DIST_DIR/$PACKAGE_NAME/config.json"
        sed -i "s|\"url\": \"http://localhost:8081/health\"|\"url\": \"$HEALTH_CHECK_URL\"|g" "$DIST_DIR/$PACKAGE_NAME/config.json"
    fi

    # 验证配置文件修改是否成功
    if ! grep -q "$MAIN_SYSTEM_URL" "$DIST_DIR/$PACKAGE_NAME/config.json"; then
        err "配置文件URL替换失败"
        exit 1
    fi

    log "生产环境配置文件生成完成"
    info "  主系统URL: $MAIN_SYSTEM_URL"
    info "  健康检查URL: $HEALTH_CHECK_URL"
}

generate_config

# 生成启动脚本
generate_scripts() {
    log "生成服务管理脚本..."

    # 生成启动脚本
    cat > "$DIST_DIR/$PACKAGE_NAME/start.sh" <<'EOF'
#!/bin/bash

# 回调接收服务启动脚本
set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色输出
G='\033[0;32m'; R='\033[0;31m'; Y='\033[1;33m'; N='\033[0m'
log() { echo -e "${G}[INFO]${N} $*"; }
err() { echo -e "${R}[ERROR]${N} $*" >&2; }
warn() { echo -e "${Y}[WARN]${N} $*"; }

# 检查配置文件
if [[ ! -f "config.json" ]]; then
    err "配置文件 config.json 不存在"
    exit 1
fi

# 检查二进制文件
for binary in callback-receiver callback-consumer; do
    if [[ ! -f "$binary" ]]; then
        err "二进制文件 $binary 不存在"
        exit 1
    fi
    if [[ ! -x "$binary" ]]; then
        warn "给 $binary 添加执行权限..."
        chmod +x "$binary"
    fi
done

# 创建日志目录
mkdir -p logs

# 检查端口占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        warn "端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 启动服务
start_service() {
    local service=$1
    local port=$2

    log "启动 $service..."

    # 检查是否已经运行
    if pgrep -f "$service" > /dev/null; then
        warn "$service 已在运行"
        return 0
    fi

    # 启动服务
    nohup ./"$service" > "logs/${service}.log" 2>&1 &
    local pid=$!

    # 等待服务启动
    sleep 2

    # 检查服务是否成功启动
    if kill -0 $pid 2>/dev/null; then
        log "$service 启动成功 (PID: $pid)"
        echo $pid > "logs/${service}.pid"
    else
        err "$service 启动失败"
        return 1
    fi
}

log "启动回调接收服务..."

# 启动主服务
start_service "callback-receiver" 8082

# 启动消费者
start_service "callback-consumer" ""

log "所有服务启动完成"
log "查看日志: tail -f logs/*.log"
log "检查状态: ./status.sh"
EOF

    # 生成停止脚本
    cat > "$DIST_DIR/$PACKAGE_NAME/stop.sh" <<'EOF'
#!/bin/bash

# 回调接收服务停止脚本
set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色输出
G='\033[0;32m'; R='\033[0;31m'; Y='\033[1;33m'; N='\033[0m'
log() { echo -e "${G}[INFO]${N} $*"; }
err() { echo -e "${R}[ERROR]${N} $*" >&2; }
warn() { echo -e "${Y}[WARN]${N} $*"; }

# 停止服务
stop_service() {
    local service=$1
    local pid_file="logs/${service}.pid"

    log "停止 $service..."

    # 通过PID文件停止
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
            if kill -0 $pid 2>/dev/null; then
                warn "强制停止 $service..."
                kill -9 $pid
            fi
        fi
        rm -f "$pid_file"
    fi

    # 通过进程名停止
    pkill -f "$service" || true

    log "$service 已停止"
}

log "停止回调接收服务..."

# 停止所有服务
stop_service "callback-receiver"
stop_service "callback-consumer"

log "所有服务已停止"
EOF

    # 生成状态检查脚本
    cat > "$DIST_DIR/$PACKAGE_NAME/status.sh" <<'EOF'
#!/bin/bash

# 回调接收服务状态检查脚本
set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色输出
G='\033[0;32m'; R='\033[0;31m'; Y='\033[1;33m'; B='\033[0;34m'; N='\033[0m'
log() { echo -e "${G}[INFO]${N} $*"; }
err() { echo -e "${R}[ERROR]${N} $*" >&2; }
warn() { echo -e "${Y}[WARN]${N} $*"; }
info() { echo -e "${B}[STATUS]${N} $*"; }

# 检查服务状态
check_service() {
    local service=$1
    local port=$2

    info "检查 $service 状态..."

    # 检查进程
    if pgrep -f "$service" > /dev/null; then
        local pid=$(pgrep -f "$service")
        log "  进程: 运行中 (PID: $pid)"
    else
        err "  进程: 未运行"
        return 1
    fi

    # 检查端口
    if [[ -n "$port" ]]; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log "  端口 $port: 监听中"
        else
            err "  端口 $port: 未监听"
            return 1
        fi
    fi

    return 0
}

# 检查健康状态
check_health() {
    info "检查服务健康状态..."

    # 检查主服务健康状态
    if curl -s http://localhost:8082/health > /dev/null 2>&1; then
        log "  主服务健康检查: 通过"
    else
        err "  主服务健康检查: 失败"
    fi

    # 检查监控端点
    if curl -s http://localhost:9090/metrics > /dev/null 2>&1; then
        log "  监控端点: 可访问"
    else
        warn "  监控端点: 不可访问"
    fi
}

echo "========================================"
echo "回调接收服务状态检查"
echo "========================================"

# 检查各个服务
check_service "callback-receiver" "8082"
check_service "callback-consumer" ""

# 检查健康状态
check_health

echo "========================================"
echo "检查完成"
echo "========================================"
EOF

    # 生成重启脚本
    cat > "$DIST_DIR/$PACKAGE_NAME/restart.sh" <<'EOF'
#!/bin/bash

# 回调接收服务重启脚本
echo "重启回调接收服务..."
./stop.sh
sleep 3
./start.sh
EOF

    # 设置脚本执行权限
    chmod +x "$DIST_DIR/$PACKAGE_NAME"/*.sh

    log "服务管理脚本生成完成"
}

generate_scripts

# 生成README文档
generate_readme() {
    log "生成部署文档..."

    cat > "$DIST_DIR/$PACKAGE_NAME/README.md" <<EOF
# 回调接收服务生产环境部署包

## 基本信息
- **版本**: $VERSION
- **构建时间**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
- **Git提交**: $GIT_COMMIT
- **Git分支**: $GIT_BRANCH

## 包含文件
- \`callback-receiver\` - 主服务 (端口: 8082)
- \`callback-consumer\` - 消费者服务
- \`config.json\` - 生产环境配置
- \`start.sh\` - 启动脚本
- \`stop.sh\` - 停止脚本
- \`restart.sh\` - 重启脚本
- \`status.sh\` - 状态检查脚本

## 生产环境配置
- **主系统URL**: $MAIN_SYSTEM_URL
- **健康检查URL**: $HEALTH_CHECK_URL
- **服务端口**: 8082
- **监控端口**: 9090

## 快速部署

### 1. 上传到生产服务器
\`\`\`bash
# 上传到生产服务器
scp $PACKAGE_NAME.tar.gz root@*************:/opt/1panel/www/sites/mywl.py258.com/

# 登录服务器并解压
ssh root@*************
cd /opt/1panel/www/sites/mywl.py258.com/
tar -xzf $PACKAGE_NAME.tar.gz
cd $PACKAGE_NAME/
\`\`\`

### 2. 启动服务
\`\`\`bash
# 一键启动
./start.sh

# 检查状态
./status.sh
\`\`\`

### 3. 验证部署
\`\`\`bash
# 检查服务健康状态
curl http://localhost:8082/health

# 检查监控指标
curl http://localhost:9090/metrics

# 测试回调接收
curl -X POST http://localhost:8082/webhook/kuaidiniao \\
  -H "Content-Type: application/json" \\
  -d '{"test": "data"}'
\`\`\`

## 支持的回调端点
- **菜鸟**: \`/webhook/cainiao\`
- **云通**: \`/webhook/yuntong\`
- **易达**: \`/webhook/yida\`
- **快递100**: \`/webhook/kuaidi100\`
- **快递鸟**: \`/webhook/kuaidiniao\`

## 服务管理

### 启动服务
\`\`\`bash
./start.sh
\`\`\`

### 停止服务
\`\`\`bash
./stop.sh
\`\`\`

### 重启服务
\`\`\`bash
./restart.sh
\`\`\`

### 检查状态
\`\`\`bash
./status.sh
\`\`\`

## 日志管理

### 查看日志
\`\`\`bash
# 实时查看所有日志
tail -f logs/*.log

# 查看主服务日志
tail -f logs/callback-receiver.log

# 查看消费者日志
tail -f logs/callback-consumer.log
\`\`\`

### 日志轮转
建议配置 logrotate 进行日志轮转：
\`\`\`bash
# /etc/logrotate.d/callback-receiver
/opt/callback-receiver/*/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
\`\`\`

## 监控和告警

### Prometheus 指标
- 访问地址: http://localhost:9090/metrics
- 主要指标:
  - \`callback_requests_total\` - 回调请求总数
  - \`callback_processing_duration\` - 回调处理耗时
  - \`callback_errors_total\` - 回调错误总数

### 健康检查
- 主服务: http://localhost:8082/health
- 监控端点: http://localhost:9090/health

## 故障排除

### 常见问题
1. **端口被占用**
   \`\`\`bash
   lsof -i :8082
   \`\`\`

2. **配置文件错误**
   \`\`\`bash
   cat config.json | jq .
   \`\`\`

3. **权限问题**
   \`\`\`bash
   chmod +x callback-receiver callback-consumer
   \`\`\`

### 调试模式
\`\`\`bash
# 前台运行查看详细日志
./callback-receiver
./callback-consumer
\`\`\`

## 升级指南

### 备份当前版本
\`\`\`bash
cp -r /opt/callback-receiver/current /opt/callback-receiver/backup-\$(date +%Y%m%d)
\`\`\`

### 部署新版本
\`\`\`bash
./stop.sh
cd ..
tar -xzf new-version.tar.gz
cd new-version/
./start.sh
\`\`\`

### 回滚
\`\`\`bash
./stop.sh
cd ../backup-YYYYMMDD/
./start.sh
\`\`\`

## 技术支持
- 项目地址: https://github.com/your-org/go-kuaidi
- 文档地址: https://docs.mywl.py258.com
- 问题反馈: https://github.com/your-org/go-kuaidi/issues
EOF

    log "部署文档生成完成"
}

# 打包部署文件
create_package() {
    log "创建部署包..."

    cd "$DIST_DIR"

    # 创建压缩包
    if ! tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"; then
        err "打包压缩失败"
        exit 1
    fi

    # 生成校验和
    if command -v sha256sum >/dev/null 2>&1; then
        sha256sum "$PACKAGE_NAME.tar.gz" > "$PACKAGE_NAME.tar.gz.sha256"
    elif command -v shasum >/dev/null 2>&1; then
        shasum -a 256 "$PACKAGE_NAME.tar.gz" > "$PACKAGE_NAME.tar.gz.sha256"
    fi

    # 显示打包结果
    PACKAGE_SIZE=$(du -sh "$PACKAGE_NAME.tar.gz" | cut -f1)

    log "部署包创建完成！"
    info "  文件名: $PACKAGE_NAME.tar.gz"
    info "  大小: $PACKAGE_SIZE"
    info "  路径: $DIST_DIR/$PACKAGE_NAME.tar.gz"

    # 显示包内容
    log "包内容预览:"
    tar -tzf "$PACKAGE_NAME.tar.gz" | head -15

    # 显示校验和
    if [[ -f "$PACKAGE_NAME.tar.gz.sha256" ]]; then
        log "SHA256校验和:"
        cat "$PACKAGE_NAME.tar.gz.sha256"
    fi
}

# 最终验证
final_verification() {
    log "执行最终验证..."

    cd "$DIST_DIR"

    # 验证压缩包
    if ! tar -tzf "$PACKAGE_NAME.tar.gz" >/dev/null; then
        err "压缩包验证失败"
        exit 1
    fi

    # 验证二进制文件
    for binary in callback-receiver callback-consumer; do
        if ! file "$PACKAGE_NAME/$binary" | grep -q "ELF.*executable"; then
            err "$binary 不是有效的 Linux 可执行文件"
            exit 1
        fi
    done

    # 验证配置文件
    if ! cat "$PACKAGE_NAME/config.json" | python3 -m json.tool >/dev/null 2>&1; then
        if ! cat "$PACKAGE_NAME/config.json" | jq . >/dev/null 2>&1; then
            err "配置文件JSON格式验证失败"
            exit 1
        fi
    fi

    log "✅ 所有验证通过！"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "========================================"
    echo "🎉 生产环境部署包构建完成！"
    echo "========================================"
    echo "版本: $VERSION"
    echo "包名: $PACKAGE_NAME.tar.gz"
    echo "大小: $(du -sh "$DIST_DIR/$PACKAGE_NAME.tar.gz" | cut -f1)"
    echo "路径: $DIST_DIR/$PACKAGE_NAME.tar.gz"
    echo ""
    echo "生产服务器部署:"
    echo "  scp $PACKAGE_NAME.tar.gz root@*************:/opt/1panel/www/sites/mywl.py258.com/"
    echo "  ssh root@*************"
    echo "  cd /opt/1panel/www/sites/mywl.py258.com/"
    echo "  tar -xzf $PACKAGE_NAME.tar.gz"
    echo "  cd $PACKAGE_NAME/"
    echo "  ./start.sh"
    echo ""
    echo "验证部署:"
    echo "  ./status.sh"
    echo "  curl https://mywl.py258.com/health"
    echo "========================================"
}

# 执行构建流程
generate_readme
create_package
final_verification
show_deployment_info